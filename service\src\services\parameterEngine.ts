/**
 * 参数替换引擎
 * 执行文本内容替换，保持原有样式和格式，支持富文本处理
 */

import fs from 'fs'
import path from 'path'
import { parameterDataService } from './externalApi'

/**
 * 替换结果接口
 */
export interface ReplaceResult {
  success: boolean
  templateData: string
  replacedElements: number
  errors: string[]
  metadata: {
    templateId: string
    dataId: string
    processedAt: string
    replacements: Array<{
      elementUuid: string
      originalText: string
      newText: string
      parameterName: string
    }>
  }
}

/**
 * 预览生成结果接口
 */
export interface PreviewResult {
  success: boolean
  previewUrl: string
  templateData: string
  width: number
  height: number
  metadata: {
    templateId: string
    dataId: string
    generatedAt: string
    fileSize?: number
  }
}

/**
 * 参数替换引擎类
 */
export class ParameterEngineService {
  
  /**
   * 执行参数替换
   */
  async replaceParameters(templateId: string, dataId: string): Promise<ReplaceResult> {
    try {
      // 获取模板数据
      const templateData = await this.loadTemplateData(templateId)
      
      // 获取用户填写的参数数据
      const userData = await this.getUserParameterData(dataId)
      
      // 执行替换
      const result = await this.performReplacement(templateData, userData, templateId, dataId)
      
      return result
    } catch (error) {
      console.error('参数替换失败:', error)
      return {
        success: false,
        templateData: '',
        replacedElements: 0,
        errors: [error.message],
        metadata: {
          templateId,
          dataId,
          processedAt: new Date().toISOString(),
          replacements: []
        }
      }
    }
  }

  /**
   * 生成参数化预览
   */
  async generatePreview(templateId: string, dataId: string): Promise<PreviewResult> {
    try {
      // 执行参数替换
      const replaceResult = await this.replaceParameters(templateId, dataId)
      
      if (!replaceResult.success) {
        throw new Error(`参数替换失败: ${replaceResult.errors.join(', ')}`)
      }

      // 获取模板基本信息
      const templateInfo = await this.getTemplateInfo(templateId)
      
      // 生成预览URL
      const previewUrl = await this.generatePreviewUrl(dataId, replaceResult.templateData)
      
      return {
        success: true,
        previewUrl,
        templateData: replaceResult.templateData,
        width: templateInfo.width,
        height: templateInfo.height,
        metadata: {
          templateId,
          dataId,
          generatedAt: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('预览生成失败:', error)
      throw new Error(`预览生成失败: ${error.message}`)
    }
  }

  /**
   * 加载模板数据
   */
  private async loadTemplateData(templateId: string): Promise<any> {
    const templatePath = path.resolve(__dirname, `../mock/templates/${templateId}.json`)
    
    if (!fs.existsSync(templatePath)) {
      throw new Error(`模板 ${templateId} 不存在`)
    }
    
    const templateContent = fs.readFileSync(templatePath, 'utf8')
    return JSON.parse(templateContent)
  }

  /**
   * 获取用户参数数据
   */
  private async getUserParameterData(dataId: string): Promise<any> {
    try {
      // 这里应该调用外部API获取用户数据
      // 暂时使用测试数据
      return this.getTestUserData(dataId)
    } catch (error) {
      console.error('获取用户数据失败:', error)
      throw new Error(`获取用户数据失败: ${error.message}`)
    }
  }

  /**
   * 获取测试用户数据
   */
  private getTestUserData(dataId: string): any {
    // 返回测试数据
    return {
      greeting: '你好,新年快乐',
      quote: '成功不是终点，失败不是致命的，<br/>重要的是继续前进的勇气。——丘吉尔',
      phone: '电话：138-0000-0000',
      address: '地址：北京市朝阳区xxx路123号',
      email: '邮箱：<EMAIL>',
      tag: '#新年快乐#',
      subtitle: 'HAPPYNEWYEAR',
      logo: 'MYLOGO'
    }
  }

  /**
   * 执行替换操作
   */
  private async performReplacement(
    templateData: any, 
    userData: any, 
    templateId: string, 
    dataId: string
  ): Promise<ReplaceResult> {
    const errors: string[] = []
    const replacements: any[] = []
    let replacedElements = 0

    try {
      // 解析模板数据
      const parsedData = JSON.parse(templateData.data)
      
      if (Array.isArray(parsedData) && parsedData[0] && parsedData[0].layers) {
        const layers = parsedData[0].layers
        
        // 遍历所有图层，查找文本元素
        for (const layer of layers) {
          if (layer.type === 'w-text' && layer.text) {
            const originalText = layer.text
            let newText = originalText
            let hasReplacement = false

            // 根据文本内容匹配参数
            const parameterName = this.matchParameterName(originalText, userData)
            
            if (parameterName && userData[parameterName]) {
              newText = this.processTextReplacement(originalText, userData[parameterName])
              hasReplacement = true
            }

            if (hasReplacement) {
              layer.text = newText
              replacedElements++
              
              replacements.push({
                elementUuid: layer.uuid,
                originalText,
                newText,
                parameterName
              })
            }
          }
        }
      }

      // 重新序列化模板数据
      const newTemplateData = {
        ...templateData,
        data: JSON.stringify(parsedData)
      }

      return {
        success: true,
        templateData: JSON.stringify(newTemplateData),
        replacedElements,
        errors,
        metadata: {
          templateId,
          dataId,
          processedAt: new Date().toISOString(),
          replacements
        }
      }
    } catch (error) {
      errors.push(`替换处理失败: ${error.message}`)
      return {
        success: false,
        templateData: JSON.stringify(templateData),
        replacedElements: 0,
        errors,
        metadata: {
          templateId,
          dataId,
          processedAt: new Date().toISOString(),
          replacements
        }
      }
    }
  }

  /**
   * 匹配参数名称
   */
  private matchParameterName(originalText: string, userData: any): string | null {
    const cleanText = originalText.replace(/<[^>]*>/g, '').toLowerCase()
    
    // 根据文本内容匹配参数
    if (cleanText.includes('你好') || cleanText.includes('hello')) {
      return 'greeting'
    }
    if (cleanText.includes('生活') || cleanText.includes('成功') || cleanText.includes('——')) {
      return 'quote'
    }
    if (cleanText.includes('电话') || cleanText.includes('phone')) {
      return 'phone'
    }
    if (cleanText.includes('地址') || cleanText.includes('address')) {
      return 'address'
    }
    if (cleanText.includes('邮箱') || cleanText.includes('email')) {
      return 'email'
    }
    if (cleanText.includes('#') && cleanText.includes('签')) {
      return 'tag'
    }
    if (cleanText.includes('hello') && cleanText.includes('november')) {
      return 'subtitle'
    }
    if (cleanText.includes('logo') || cleanText.includes('your')) {
      return 'logo'
    }
    
    return null
  }

  /**
   * 处理文本替换
   */
  private processTextReplacement(originalText: string, newValue: string): string {
    // 保持HTML标签
    if (originalText.includes('<br/>') && !newValue.includes('<br/>')) {
      // 如果原文本有换行但新值没有，尝试智能添加换行
      if (newValue.length > 30) {
        const midPoint = Math.floor(newValue.length / 2)
        const spaceIndex = newValue.indexOf(' ', midPoint)
        if (spaceIndex > 0) {
          return newValue.substring(0, spaceIndex) + '<br/>' + newValue.substring(spaceIndex + 1)
        }
      }
    }
    
    return newValue
  }

  /**
   * 获取模板基本信息
   */
  private async getTemplateInfo(templateId: string): Promise<any> {
    const templateData = await this.loadTemplateData(templateId)
    return {
      width: templateData.width || 1242,
      height: templateData.height || 2208,
      title: templateData.title || '未知模板'
    }
  }

  /**
   * 生成预览URL
   */
  private async generatePreviewUrl(dataId: string, templateData: string): Promise<string> {
    // 这里应该保存替换后的模板数据，并返回预览URL
    // 暂时返回一个模拟的URL
    return `http://localhost:7001/preview/parameter/${dataId}`
  }
}

export default new ParameterEngineService()
