/**
 * 模板解析服务API测试
 * 测试模板解析功能和参数候选项生成
 */

const axios = require('axios');
const { expect } = require('chai');

const BASE_URL = 'http://localhost:7001';
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000
});

describe('模板解析服务API测试', () => {
  
  describe('POST /api/template/parse - 解析模板内容', () => {
    
    it('应该成功解析模板ID=2', async () => {
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      
      expect(response.status).to.equal(200);
      expect(response.data).to.have.property('code', 200);
      expect(response.data.data).to.have.property('templateId', '2');
      expect(response.data.data).to.have.property('templateTitle');
      expect(response.data.data).to.have.property('textElements');
      expect(response.data.data).to.have.property('parameterCandidates');
      expect(response.data.data).to.have.property('summary');
    });
    
    it('应该识别出5个文本元素', async () => {
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      
      expect(response.data.data.textElements).to.be.an('array');
      expect(response.data.data.textElements.length).to.equal(5);
      expect(response.data.data.summary.totalTextElements).to.equal(5);
    });
    
    it('应该为每个文本元素生成参数候选项', async () => {
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      
      expect(response.data.data.parameterCandidates).to.be.an('array');
      expect(response.data.data.parameterCandidates.length).to.equal(5);
      expect(response.data.data.summary.totalParameterCandidates).to.equal(5);
    });
    
    it('应该正确识别文本元素的属性', async () => {
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      const textElement = response.data.data.textElements[0];
      
      expect(textElement).to.have.property('uuid');
      expect(textElement).to.have.property('type', 'w-text');
      expect(textElement).to.have.property('text');
      expect(textElement).to.have.property('position');
      expect(textElement).to.have.property('style');
      
      // 验证位置信息
      expect(textElement.position).to.have.property('left');
      expect(textElement.position).to.have.property('top');
      expect(textElement.position).to.have.property('width');
      expect(textElement.position).to.have.property('height');
      
      // 验证样式信息
      expect(textElement.style).to.have.property('fontSize');
      expect(textElement.style).to.have.property('color');
      expect(textElement.style).to.have.property('textAlign');
    });
    
    it('应该正确生成参数候选项属性', async () => {
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      const parameterCandidate = response.data.data.parameterCandidates[0];
      
      expect(parameterCandidate).to.have.property('elementUuid');
      expect(parameterCandidate).to.have.property('suggestedName');
      expect(parameterCandidate).to.have.property('suggestedLabel');
      expect(parameterCandidate).to.have.property('suggestedType');
      expect(parameterCandidate).to.have.property('originalText');
      expect(parameterCandidate).to.have.property('textCategory');
      expect(parameterCandidate).to.have.property('isRequired');
    });
    
  });
  
  describe('文本内容分类测试', () => {
    
    it('应该正确识别电话号码', async () => {
      // 这里需要模拟包含电话号码的模板
      // 实际测试中需要准备相应的测试数据
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      
      // 查找电话号码类型的参数
      const phoneParameter = response.data.data.parameterCandidates.find(
        param => param.textCategory === 'phone'
      );
      
      if (phoneParameter) {
        expect(phoneParameter.suggestedType).to.equal('text');
        expect(phoneParameter.suggestedName).to.include('phone');
      }
    });
    
    it('应该正确识别地址信息', async () => {
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      
      // 查找地址类型的参数
      const addressParameter = response.data.data.parameterCandidates.find(
        param => param.textCategory === 'address'
      );
      
      if (addressParameter) {
        expect(addressParameter.suggestedType).to.equal('text');
        expect(addressParameter.suggestedName).to.include('address');
      }
    });
    
    it('应该正确识别邮箱地址', async () => {
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      
      // 查找邮箱类型的参数
      const emailParameter = response.data.data.parameterCandidates.find(
        param => param.textCategory === 'email'
      );
      
      if (emailParameter) {
        expect(emailParameter.suggestedType).to.equal('email');
        expect(emailParameter.suggestedName).to.include('email');
      }
    });
    
    it('应该正确分类普通文本', async () => {
      const requestData = {
        templateId: '2'
      };
      
      const response = await api.post('/api/template/parse', requestData);
      
      // 查找普通文本类型的参数
      const generalParameter = response.data.data.parameterCandidates.find(
        param => param.textCategory === 'general'
      );
      
      expect(generalParameter).to.exist;
      expect(generalParameter.suggestedType).to.equal('text');
    });
    
  });
  
  describe('错误处理测试', () => {
    
    it('应该处理无效的模板ID', async () => {
      const requestData = {
        templateId: '999999'
      };
      
      try {
        await api.post('/api/template/parse', requestData);
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.response.status).to.be.oneOf([400, 404]);
        expect(error.response.data.code).to.be.oneOf([400, 404]);
      }
    });
    
    it('应该处理缺少templateId参数', async () => {
      const requestData = {};
      
      try {
        await api.post('/api/template/parse', requestData);
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.response.status).to.equal(400);
        expect(error.response.data.code).to.equal(400);
      }
    });
    
    it('应该处理空的templateId', async () => {
      const requestData = {
        templateId: ''
      };
      
      try {
        await api.post('/api/template/parse', requestData);
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.response.status).to.equal(400);
        expect(error.response.data.code).to.equal(400);
      }
    });
    
  });
  
});
