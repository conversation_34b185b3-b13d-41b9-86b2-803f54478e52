/**
 * 模板数据服务API测试
 * 测试模板列表、详情、预览等接口功能
 */

const axios = require('axios');
const { expect } = require('chai');

const BASE_URL = 'http://localhost:7001';
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000
});

describe('模板数据服务API测试', () => {
  
  describe('GET /api/templates - 获取模板列表', () => {
    
    it('应该返回模板列表', async () => {
      const response = await api.get('/api/templates');
      
      expect(response.status).to.equal(200);
      expect(response.data).to.have.property('code', 200);
      expect(response.data).to.have.property('data');
      expect(response.data.data).to.have.property('list');
      expect(response.data.data).to.have.property('total');
      expect(response.data.data).to.have.property('page');
      expect(response.data.data).to.have.property('pageSize');
    });
    
    it('应该支持分页参数', async () => {
      const response = await api.get('/api/templates?page=1&pageSize=5');
      
      expect(response.status).to.equal(200);
      expect(response.data.data.page).to.equal(1);
      expect(response.data.data.pageSize).to.equal(5);
      expect(response.data.data.list.length).to.be.at.most(5);
    });
    
    it('应该支持分类筛选', async () => {
      const response = await api.get('/api/templates?category=poster');
      
      expect(response.status).to.equal(200);
      expect(response.data.data.list).to.be.an('array');
    });
    
    it('应该支持关键词搜索', async () => {
      const response = await api.get('/api/templates?keyword=日签');
      
      expect(response.status).to.equal(200);
      expect(response.data.data.list).to.be.an('array');
    });
    
  });
  
  describe('GET /api/template/:templateId - 获取模板详情', () => {
    
    it('应该返回指定模板的详细信息', async () => {
      const templateId = '2';
      const response = await api.get(`/api/template/${templateId}`);
      
      expect(response.status).to.equal(200);
      expect(response.data).to.have.property('code', 200);
      expect(response.data.data).to.have.property('id', templateId);
      expect(response.data.data).to.have.property('title');
      expect(response.data.data).to.have.property('description');
      expect(response.data.data).to.have.property('thumbnail');
      expect(response.data.data).to.have.property('category');
      expect(response.data.data).to.have.property('width');
      expect(response.data.data).to.have.property('height');
    });
    
    it('应该在模板不存在时返回404', async () => {
      try {
        await api.get('/api/template/999999');
        expect.fail('应该抛出404错误');
      } catch (error) {
        expect(error.response.status).to.equal(404);
        expect(error.response.data.code).to.equal(404);
      }
    });
    
  });
  
  describe('GET /api/template/:templateId/preview - 获取模板预览', () => {
    
    it('应该生成模板预览图片', async () => {
      const templateId = '2';
      const response = await api.get(`/api/template/${templateId}/preview`);
      
      expect(response.status).to.equal(200);
      expect(response.data).to.have.property('code', 200);
      expect(response.data.data).to.have.property('previewUrl');
      expect(response.data.data).to.have.property('width');
      expect(response.data.data).to.have.property('height');
      expect(response.data.data.previewUrl).to.include('http');
    });
    
    it('应该支持自定义预览尺寸', async () => {
      const templateId = '2';
      const width = 400;
      const height = 600;
      const response = await api.get(`/api/template/${templateId}/preview?width=${width}&height=${height}`);
      
      expect(response.status).to.equal(200);
      expect(response.data.data.width).to.equal(width);
      expect(response.data.data.height).to.equal(height);
    });
    
    it('应该支持图片质量参数', async () => {
      const templateId = '2';
      const quality = 0.8;
      const response = await api.get(`/api/template/${templateId}/preview?quality=${quality}`);
      
      expect(response.status).to.equal(200);
      expect(response.data.data).to.have.property('previewUrl');
    });
    
  });
  
  describe('错误处理测试', () => {
    
    it('应该处理无效的模板ID', async () => {
      try {
        await api.get('/api/template/invalid-id');
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.response.status).to.be.oneOf([400, 404]);
      }
    });
    
    it('应该处理无效的分页参数', async () => {
      const response = await api.get('/api/templates?page=-1&pageSize=0');
      
      // 应该使用默认值或返回错误
      expect(response.status).to.be.oneOf([200, 400]);
    });
    
  });
  
});
