# 迅排设计 API 接口文档

## 概述

本文档描述了迅排设计项目的后端API接口，包括截图服务、文件上传、模板管理、素材管理等功能。

### 基础信息
- **基础URL**: `http://localhost:7001` (开发环境)
- **API前缀**: `/api`
- **认证方式**: Bear<PERSON> (Authorization Header)
- **响应格式**: JSON

### 通用响应格式
```json
{
  "code": 200,
  "msg": "success",
  "result": {
    // 具体数据
  }
}
```

## 截图服务 API

### 1. 设计稿截图
**接口地址**: `GET /api/screenshots`

**功能描述**: 对设计稿进行截图，支持模板和组件截图

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | String/Number | 可选 | 截图id，高优先级 |
| tempid | String/Number | 可选 | 模板id，低优先级，无id时取该值 |
| tempType | String/Number | 可选 | 区分模板和组件类型，临时用 |
| width | String | 必填 | 视窗宽度 |
| height | String | 必填 | 视窗高度 |
| screenshot_url | String | 可选 | 自定义截图页面URL |
| type | String | 可选 | file=正常截图返回，cover=封面生成，默认file |
| size | String | 可选 | 按比例缩小到指定宽度 |
| quality | String | 可选 | 图片质量 1-100 |
| index | String/Number | 可选 | 下载哪个画板，默认0 |

**响应示例**:
```
Content-Type: image/jpg
// 返回图片二进制数据
```

### 2. 网页全屏截图
**接口地址**: `GET /api/printscreen`

**功能描述**: 对任意网页进行全屏截图

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| url | String | 必填 | 目标网页链接 |
| width | String | 可选 | 视窗宽度，默认375 |
| height | String | 可选 | 视窗高度，默认0 |
| prevent | Boolean | 可选 | 是否阻止立即截图，默认false |
| type | String | 可选 | file=返回二进制文件，cover=返回地址，默认file |
| size | String | 可选 | 等比缩放到指定宽度（仅type=cover生效） |
| quality | String | 可选 | 压缩质量1-100（仅有size时生效） |
| wait | Number | 可选 | 截图前等待时间，单位ms |
| ua | String | 可选 | 模拟设备User-Agent |
| devices | String | 可选 | 套用设备预设，如"iPhone 6" |
| scale | Number | 可选 | 设备像素比(DPR)，范围1-4，默认1 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "截图成功",
  "data": {
    "path": "/cache/screenshot_1234567890_uuid.png",
    "thumbPath": "/cache/screenshot_1234567890_uuid.jpg"
  }
}
```

## 文件管理 API

### 3. 文件上传
**接口地址**: `POST /api/file/upload`

**功能描述**: 上传文件到服务器

**请求参数** (multipart/form-data):
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 必填 | 二进制文件 |
| folder | String | 可选 | 目标文件夹，空为根目录 |
| name | String | 可选 | 文件名，默认随机生成 |

**响应示例**:
```json
{
  "code": 200,
  "result": {
    "key": "folder/filename.jpg",
    "url": "http://localhost:7001/static/folder/filename.jpg"
  }
}
```

## 设计管理 API

### 4. 获取模板列表
**接口地址**: `GET /design/list`

**功能描述**: 获取模板列表（虚拟数据）

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cate | String | 可选 | 分类ID |
| type | Number | 可选 | 类型：0=模板，1=组件 |

**响应示例**:
```json
{
  "code": 200,
  "result": {
    "list": [
      {
        "id": "template001",
        "title": "海报模板1",
        "cover": "http://localhost:7001/static/template001-cover.jpg",
        "width": 750,
        "height": 1334
      }
    ]
  }
}
```

### 5. 获取模板详情
**接口地址**: `GET /design/temp`

**功能描述**: 获取模板详细数据（虚拟数据）

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | String | 必填 | 模板ID |
| type | Number | 可选 | 类型：0=模板，1=组件 |
| cate | String | 可选 | 分类ID |

**响应示例**:
```json
{
  "code": 200,
  "result": {
    "id": "template001",
    "title": "海报模板1",
    "width": 750,
    "height": 1334,
    "data": {
      // 模板设计数据
    }
  }
}
```

### 6. 保存模板
**接口地址**: `POST /design/edit`

**功能描述**: 保存模板数据（虚拟数据）

**请求参数** (JSON):
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | String | 可选 | 模板ID，不传则新增 |
| title | String | 必填 | 模板标题 |
| data | Object | 必填 | 模板设计数据 |
| width | Number | 必填 | 画布宽度 |
| height | Number | 必填 | 画布高度 |
| type | Number | 可选 | 类型：0=模板，1=组件 |
| cate | String | 可选 | 分类ID |
| tag | String | 可选 | 标签 |

**响应示例**:
```json
{
  "code": 200,
  "result": {
    "id": "template001"
  }
}
```

## 素材管理 API

### 7. 获取素材列表
**接口地址**: `GET /design/material`

**功能描述**: 获取素材列表（虚拟数据）

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cate | String | 必填 | 素材分类 |

**响应示例**:
```json
{
  "code": 200,
  "result": {
    "list": [
      {
        "id": 1,
        "title": "素材1",
        "url": "http://example.com/material1.svg",
        "thumb": "http://example.com/material1_thumb.jpg"
      }
    ]
  }
}
```

### 8. 获取照片素材
**接口地址**: `GET /design/imgs`

**功能描述**: 获取照片素材列表（虚拟数据）

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| cate | String | 必填 | 照片分类 |

**响应示例**:
```json
{
  "code": 200,
  "result": {
    "list": [
      {
        "id": 1,
        "title": "照片1",
        "url": "http://example.com/photo1.jpg",
        "thumb": "http://example.com/photo1_thumb.jpg"
      }
    ]
  }
}
```

## 用户管理 API

### 9. 获取用户上传列表
**接口地址**: `GET /design/user/image`

**功能描述**: 获取用户上传的图片列表（虚拟数据）

**请求参数**: 无

**响应示例**:
```json
{
  "code": 200,
  "result": {
    "list": [
      {
        "name": "user_image1.jpg",
        "url": "http://localhost:7001/static/user/user_image1.jpg",
        "size": 1024000
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 401 | 未授权，token无效 |
| 500 | 服务器内部错误 |

## 注意事项

1. **开发环境配置**: 本文档基于开发环境配置，生产环境需要相应调整URL和配置
2. **虚拟数据**: 部分接口使用虚拟数据，实际生产环境需要连接真实数据库
3. **文件路径**: 上传文件保存在 `/static/` 目录下，可通过HTTP直接访问
4. **截图服务**: 依赖Puppeteer，需要Chrome浏览器环境
5. **队列限制**: 截图服务有队列限制，避免服务器过载

## 前端调用示例

```typescript
// 获取模板列表
import api from '@/api'

// 获取模板列表
const getTemplateList = async () => {
  const result = await api.home.getTemplateList({
    page: 1,
    pageSize: 20,
    cate: 'poster'
  })
  return result
}

// 上传文件
const uploadFile = async (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('folder', 'user')
  
  const result = await fetch('/api/file/upload', {
    method: 'POST',
    body: formData
  })
  return result.json()
}

// 截图下载
const downloadScreenshot = (id: string, width: number, height: number) => {
  const url = `/api/screenshots?id=${id}&width=${width}&height=${height}`
  window.open(url, '_blank')
}
```
