# 动态参数模板系统测试目录

## 目录结构

```
test/
├── README.md                    # 测试说明文档
├── api/                         # API接口测试文件
│   ├── template.test.js         # 模板相关接口测试
│   ├── parser.test.js           # 模板解析接口测试
│   ├── parameter.test.js        # 参数化功能测试
│   ├── screenshot.test.js       # 截图生成测试
│   └── external-api.test.js     # 外部API集成测试
├── scripts/                     # 测试脚本文件
│   ├── api-test.sh             # API批量测试脚本
│   ├── performance-test.sh     # 性能测试脚本
│   ├── setup-test-env.sh       # 测试环境准备脚本
│   └── cleanup-test-data.sh    # 测试数据清理脚本
├── data/                       # 测试数据文件
│   ├── templates/              # 模板测试数据
│   ├── parameters/             # 参数配置测试数据
│   ├── user-data/              # 用户填写数据
│   └── expected-results/       # 预期结果数据
└── results/                    # 测试结果文件
    ├── api-test-results/       # API测试结果
    ├── performance-reports/    # 性能测试报告
    ├── screenshots/            # 测试生成的截图
    └── logs/                   # 测试日志
```

## 测试环境要求

- Node.js 16.x 或更高版本
- npm 8.x 或更高版本
- 项目服务运行在 http://localhost:7001

## 运行测试

### 1. 准备测试环境
```bash
cd test
chmod +x scripts/*.sh
./scripts/setup-test-env.sh
```

### 2. 运行API测试
```bash
# 运行所有API测试
npm test

# 运行特定测试文件
npm test api/template.test.js
```

### 3. 运行性能测试
```bash
./scripts/performance-test.sh
```

### 4. 清理测试数据
```bash
./scripts/cleanup-test-data.sh
```

## 测试覆盖范围

### API接口测试
- 模板数据服务接口
- 模板解析服务接口
- 参数化预览接口
- 图片生成接口
- 批量处理接口

### 功能测试
- 模板解析准确性
- 参数替换正确性
- 图片生成质量
- 富文本处理

### 性能测试
- API响应时间
- 并发处理能力
- 内存和CPU使用
- 批量生成性能

### 集成测试
- 端到端业务流程
- 外部API集成
- 错误处理机制

## 测试数据说明

### 标准测试模板
- 模板ID=2：用于基础功能测试
- 包含5个文本元素的完整模板
- 支持多种文本类型识别测试

### 参数配置数据
- 标准参数配置示例
- 各种参数类型的测试用例
- 边界条件测试数据

### 用户填写数据
- 正常数据填写示例
- 异常数据测试用例
- 特殊字符处理测试

## 测试报告

测试完成后，结果将保存在 `results/` 目录下：
- API测试结果：JSON格式的详细测试报告
- 性能测试报告：包含响应时间、吞吐量等指标
- 截图对比：生成的图片与预期结果对比
- 错误日志：测试过程中的错误和警告信息
