# 动态参数模板系统API批量测试脚本 (PowerShell版本)

# 配置
$BASE_URL = "http://localhost:7001"
$TEST_TEMPLATE_ID = "2"
$TEST_DATA_ID = "test-data-123"
$OUTPUT_DIR = "./test/results/api-test-results"

# 创建输出目录
if (!(Test-Path $OUTPUT_DIR)) {
    New-Item -ItemType Directory -Path $OUTPUT_DIR -Force | Out-Null
}

# 日志函数
function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warn {
    param($Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 测试函数
function Test-API {
    param(
        [string]$Method,
        [string]$Endpoint,
        [string]$Body = $null,
        [string]$Description,
        [string]$OutputFile
    )
    
    Write-Info "测试: $Description"
    
    try {
        $params = @{
            Uri = "$BASE_URL$Endpoint"
            Method = $Method
            ContentType = "application/json"
        }
        
        if ($Body) {
            $params.Body = $Body
        }
        
        $response = Invoke-WebRequest @params
        $response.Content | Out-File -FilePath $OutputFile -Encoding UTF8
        
        if ($response.StatusCode -ge 200 -and $response.StatusCode -lt 300) {
            Write-Info "✓ $Description - HTTP $($response.StatusCode)"
            return $true
        } else {
            Write-Error "✗ $Description - HTTP $($response.StatusCode)"
            return $false
        }
    } catch {
        Write-Error "✗ $Description - 错误: $($_.Exception.Message)"
        $_.Exception.Message | Out-File -FilePath $OutputFile -Encoding UTF8
        return $false
    }
}

# 开始测试
Write-Info "开始API批量测试..."
Write-Info "基础URL: $BASE_URL"
Write-Info "输出目录: $OUTPUT_DIR"

$testResults = @()

# 1. 模板数据服务测试
Write-Info "=== 模板数据服务测试 ==="

# 1.1 获取模板列表
$result = Test-API -Method "GET" -Endpoint "/api/templates" -Description "获取模板列表" -OutputFile "$OUTPUT_DIR/templates_list.json"
$testResults += @{ Test = "获取模板列表"; Result = $result }

# 1.2 获取模板列表（带分页）
$result = Test-API -Method "GET" -Endpoint "/api/templates?page=1&pageSize=5" -Description "获取模板列表（分页）" -OutputFile "$OUTPUT_DIR/templates_list_paged.json"
$testResults += @{ Test = "获取模板列表（分页）"; Result = $result }

# 1.3 获取模板详情
$result = Test-API -Method "GET" -Endpoint "/api/template?id=$TEST_TEMPLATE_ID" -Description "获取模板详情" -OutputFile "$OUTPUT_DIR/template_detail.json"
$testResults += @{ Test = "获取模板详情"; Result = $result }

# 1.4 获取模板预览
$result = Test-API -Method "GET" -Endpoint "/api/template/preview?id=$TEST_TEMPLATE_ID" -Description "获取模板预览" -OutputFile "$OUTPUT_DIR/template_preview.json"
$testResults += @{ Test = "获取模板预览"; Result = $result }

# 2. 模板解析服务测试
Write-Info "=== 模板解析服务测试 ==="

# 2.1 解析模板
$body = @{ templateId = $TEST_TEMPLATE_ID } | ConvertTo-Json
$result = Test-API -Method "POST" -Endpoint "/api/template/parse" -Body $body -Description "解析模板内容" -OutputFile "$OUTPUT_DIR/template_parse.json"
$testResults += @{ Test = "解析模板内容"; Result = $result }

# 3. 参数化预览服务测试
Write-Info "=== 参数化预览服务测试 ==="

# 3.1 生成参数化预览
$body = @{ templateId = $TEST_TEMPLATE_ID; parameterDataId = $TEST_DATA_ID } | ConvertTo-Json
$result = Test-API -Method "POST" -Endpoint "/api/parameter/preview" -Body $body -Description "生成参数化预览" -OutputFile "$OUTPUT_DIR/parameter_preview.json"
$testResults += @{ Test = "生成参数化预览"; Result = $result }

# 3.2 执行参数替换
$body = @{ templateId = $TEST_TEMPLATE_ID; parameterDataId = $TEST_DATA_ID } | ConvertTo-Json
$result = Test-API -Method "POST" -Endpoint "/api/parameter/replace" -Body $body -Description "执行参数替换" -OutputFile "$OUTPUT_DIR/parameter_replace.json"
$testResults += @{ Test = "执行参数替换"; Result = $result }

# 3.3 访问预览页面
$result = Test-API -Method "GET" -Endpoint "/preview/parameter/$TEST_DATA_ID" -Description "访问预览页面" -OutputFile "$OUTPUT_DIR/preview_page.html"
$testResults += @{ Test = "访问预览页面"; Result = $result }

# 4. 图片生成服务测试
Write-Info "=== 图片生成服务测试 ==="

# 4.1 参数化截图
$result = Test-API -Method "GET" -Endpoint "/api/screenshots?parameterDataId=$TEST_DATA_ID&width=1242&height=2208" -Description "生成参数化截图" -OutputFile "$OUTPUT_DIR/screenshot.json"
$testResults += @{ Test = "生成参数化截图"; Result = $result }

# 4.2 批量生成图片
$body = @{ 
    dataIds = @($TEST_DATA_ID)
    outputOptions = @{
        width = 1242
        height = 2208
        type = "file"
        size = 2
        quality = 0.9
    }
} | ConvertTo-Json -Depth 3
$result = Test-API -Method "POST" -Endpoint "/api/parameter/batch-generate" -Body $body -Description "批量生成图片" -OutputFile "$OUTPUT_DIR/batch_generate.json"
$testResults += @{ Test = "批量生成图片"; Result = $result }

# 5. 健康检查测试
Write-Info "=== 健康检查测试 ==="

# 5.1 系统健康检查
$result = Test-API -Method "GET" -Endpoint "/health" -Description "系统健康检查" -OutputFile "$OUTPUT_DIR/health_check.json"
$testResults += @{ Test = "系统健康检查"; Result = $result }

# 5.2 缓存状态查询
$result = Test-API -Method "GET" -Endpoint "/api/health/cache" -Description "缓存状态查询" -OutputFile "$OUTPUT_DIR/cache_status.json"
$testResults += @{ Test = "缓存状态查询"; Result = $result }

# 6. 错误处理测试
Write-Info "=== 错误处理测试 ==="

# 6.1 无效模板ID
$result = Test-API -Method "GET" -Endpoint "/api/template?id=999999" -Description "无效模板ID测试" -OutputFile "$OUTPUT_DIR/error_invalid_template.json"
$testResults += @{ Test = "无效模板ID测试"; Result = $result }

# 6.2 缺少参数
$body = @{} | ConvertTo-Json
$result = Test-API -Method "POST" -Endpoint "/api/template/parse" -Body $body -Description "缺少参数测试" -OutputFile "$OUTPUT_DIR/error_missing_params.json"
$testResults += @{ Test = "缺少参数测试"; Result = $result }

# 生成测试报告
Write-Info "=== 生成测试报告 ==="

$successCount = ($testResults | Where-Object { $_.Result -eq $true }).Count
$totalCount = $testResults.Count
$successRate = [math]::Round(($successCount / $totalCount) * 100, 2)

$report = @"
# API测试报告

## 测试时间
$(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

## 测试环境
- 基础URL: $BASE_URL
- 测试模板ID: $TEST_TEMPLATE_ID
- 测试数据ID: $TEST_DATA_ID

## 测试结果统计
- 总测试数: $totalCount
- 成功数: $successCount
- 失败数: $($totalCount - $successCount)
- 成功率: $successRate%

## 详细结果

| 测试项目 | 结果 |
|---------|------|
"@

foreach ($test in $testResults) {
    $status = if ($test.Result) { "✓ 通过" } else { "✗ 失败" }
    $report += "`n| $($test.Test) | $status |"
}

$report += @"

## 详细结果文件
所有API响应结果已保存在 $OUTPUT_DIR 目录下的JSON文件中。

## 建议
"@

if ($successRate -lt 100) {
    $report += "`n- 请检查失败的测试项目，确保服务正常运行"
    $report += "`n- 查看详细错误信息在对应的输出文件中"
}

if ($successRate -ge 90) {
    $report += "`n- 系统运行良好，大部分功能正常"
} elseif ($successRate -ge 70) {
    $report += "`n- 系统基本正常，但有部分功能需要检查"
} else {
    $report += "`n- 系统存在较多问题，需要全面检查"
}

$report | Out-File -FilePath "$OUTPUT_DIR/test_report.md" -Encoding UTF8

Write-Info "测试完成！"
Write-Info "成功率: $successRate% ($successCount/$totalCount)"
Write-Info "测试报告: $OUTPUT_DIR/test_report.md"
Write-Info "详细结果: $OUTPUT_DIR/*.json"

# 显示测试结果摘要
Write-Host "`n=== 测试结果摘要 ===" -ForegroundColor Cyan
foreach ($test in $testResults) {
    $color = if ($test.Result) { "Green" } else { "Red" }
    $status = if ($test.Result) { "✓" } else { "✗" }
    Write-Host "$status $($test.Test)" -ForegroundColor $color
}
