# 动态参数模板系统API接口规范

## 1. 接口概述

### 1.1 基础信息
- **基础URL**：`http://localhost:7001` (开发环境)
- **协议**：HTTP/HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8
- **认证方式**：Bearer Token (外部API调用)

### 1.2 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  }
}
```

### 1.3 错误码说明
- **200**：成功
- **400**：请求参数错误
- **401**：认证失败
- **404**：资源不存在
- **500**：服务器内部错误

## 2. 模板数据服务接口

### 2.1 获取模板列表
**接口地址**：`GET /api/templates`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| page | number | 否 | 页码，默认1 | 1 |
| pageSize | number | 否 | 每页数量，默认10 | 10 |
| category | string | 否 | 模板分类 | "poster" |
| keyword | string | 否 | 搜索关键词 | "日签" |

**请求示例**：
```bash
GET /api/templates?page=1&pageSize=10&category=poster
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "1",
        "title": "日签插画手机海报",
        "description": "适用于日签分享的插画风格海报",
        "thumbnail": "http://localhost:7001/static/thumbnails/1.jpg",
        "category": "poster",
        "tags": ["日签", "插画", "手机"],
        "width": 1242,
        "height": 2208,
        "createdAt": "2025-01-16T10:00:00Z",
        "updatedAt": "2025-01-16T10:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "totalPages": 10
  }
}
```

### 2.2 获取模板详情
**接口地址**：`GET /api/template/:templateId`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| templateId | string | 是 | 模板ID | "2" |

**请求示例**：
```bash
GET /api/template/2
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "2",
    "title": "示例模板 - 日签插画手机海报",
    "description": "这是一个用于演示的日签插画手机海报模板",
    "thumbnail": "http://localhost:7001/static/thumbnails/2.jpg",
    "category": "poster",
    "tags": ["日签", "插画", "手机", "问候"],
    "width": 1242,
    "height": 2208,
    "textElementsCount": 5,
    "createdAt": "2025-01-16T10:00:00Z",
    "updatedAt": "2025-01-16T10:00:00Z",
    "metadata": {
      "author": "设计师",
      "version": "1.0",
      "license": "免费商用"
    }
  }
}
```

### 2.3 获取模板预览
**接口地址**：`GET /api/template/:templateId/preview`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| templateId | string | 是 | 模板ID | "2" |

**查询参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| width | number | 否 | 预览图宽度 | 400 |
| height | number | 否 | 预览图高度 | 600 |
| quality | number | 否 | 图片质量0-1 | 0.8 |

**请求示例**：
```bash
GET /api/template/2/preview?width=400&height=600&quality=0.8
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "previewUrl": "http://localhost:7001/static/previews/2_400x600.jpg",
    "width": 400,
    "height": 600,
    "fileSize": 45678,
    "generatedAt": "2025-01-16T10:00:00Z"
  }
}
```

## 3. 模板解析服务接口

### 3.1 解析模板内容
**接口地址**：`POST /api/template/parse`

**请求参数**：
```json
{
  "templateId": "2"
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| templateId | string | 是 | 模板ID | "2" |

**请求示例**：
```bash
POST /api/template/parse
Content-Type: application/json

{
  "templateId": "2"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "templateId": "2",
    "templateTitle": "示例模板 - 日签插画手机海报",
    "textElements": [
      {
        "uuid": "98fd9b16db8a",
        "type": "w-text",
        "text": "你好,十二月",
        "position": {
          "left": 84.11,
          "top": 289.4,
          "width": 1092.38,
          "height": 211
        },
        "style": {
          "fontSize": 176,
          "color": "#000000ff",
          "fontFamily": "PingFangSC-Regular",
          "textAlign": "center",
          "lineHeight": 1.2,
          "letterSpacing": 0,
          "fontWeight": "normal",
          "fontStyle": "normal"
        }
      }
    ],
    "parameterCandidates": [
      {
        "elementUuid": "98fd9b16db8a",
        "suggestedName": "greeting_text",
        "suggestedLabel": "问候语",
        "suggestedDescription": "个性化的问候文字",
        "suggestedType": "text",
        "originalText": "你好,十二月",
        "textCategory": "general",
        "maxLength": 20,
        "isRequired": true
      }
    ],
    "summary": {
      "totalTextElements": 5,
      "totalParameterCandidates": 5,
      "categories": {
        "general": 3,
        "phone": 1,
        "address": 1
      }
    }
  }
}
```

## 4. 参数化预览服务接口

### 4.1 生成参数化预览
**接口地址**：`POST /api/parameter/preview`

**请求参数**：
```json
{
  "templateId": "2",
  "parameterDataId": "user-data-123"
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| templateId | string | 是 | 模板ID | "2" |
| parameterDataId | string | 是 | 参数数据ID | "user-data-123" |

**请求示例**：
```bash
POST /api/parameter/preview
Content-Type: application/json

{
  "templateId": "2",
  "parameterDataId": "user-data-123"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "previewUrl": "http://localhost:7001/preview/parameter/user-data-123",
    "modifiedTemplateData": {
      "id": "2",
      "title": "示例模板 - 日签插画手机海报",
      "data": "{\"page\":{...},\"widgets\":[...]}"
    },
    "appliedParameters": [
      {
        "elementUuid": "98fd9b16db8a",
        "parameterName": "greeting",
        "originalValue": "你好,十二月",
        "newValue": "你好,新年快乐"
      }
    ],
    "generatedAt": "2025-01-16T10:00:00Z"
  }
}
```

### 4.2 参数化预览页面
**接口地址**：`GET /preview/parameter/:dataId`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| dataId | string | 是 | 参数数据ID | "user-data-123" |

**请求示例**：
```bash
GET /preview/parameter/user-data-123
```

**响应**：返回HTML页面，包含参数化后的模板预览内容

## 5. 图片生成服务接口

### 5.1 参数化截图
**接口地址**：`GET /api/screenshots`

**查询参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| parameterDataId | string | 是 | 参数数据ID | "user-data-123" |
| width | number | 否 | 图片宽度 | 1242 |
| height | number | 否 | 图片高度 | 2208 |
| type | string | 否 | 输出类型，默认file | "file" |
| size | number | 否 | 图片尺寸倍数，默认2 | 2 |
| quality | number | 否 | 图片质量0-1，默认0.9 | 0.9 |

**请求示例**：
```bash
GET /api/screenshots?parameterDataId=user-data-123&width=1242&height=2208&type=file&size=2&quality=0.9
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "url": "http://localhost:7001/static/generated/user-data-123_1242x2208.jpg",
    "width": 1242,
    "height": 2208,
    "fileSize": 234567,
    "quality": 0.9,
    "generatedAt": "2025-01-16T10:00:00Z",
    "metadata": {
      "templateId": "2",
      "parameterDataId": "user-data-123",
      "generationTime": 3.2
    }
  }
}
```

### 5.2 批量生成图片
**接口地址**：`POST /api/parameter/batch-generate`

**请求参数**：
```json
{
  "dataIds": ["user-data-123", "user-data-124", "user-data-125"],
  "outputOptions": {
    "width": 1242,
    "height": 2208,
    "type": "file",
    "size": 2,
    "quality": 0.9
  }
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| dataIds | array | 是 | 参数数据ID数组 | ["user-data-123"] |
| outputOptions | object | 是 | 输出选项 | 见上方示例 |

**请求示例**：
```bash
POST /api/parameter/batch-generate
Content-Type: application/json

{
  "dataIds": ["user-data-123", "user-data-124"],
  "outputOptions": {
    "width": 1242,
    "height": 2208,
    "type": "file",
    "size": 2,
    "quality": 0.9
  }
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "batchId": "batch_1642320000000_abc123",
    "status": "processing",
    "totalCount": 2,
    "estimatedTime": 10,
    "createdAt": "2025-01-16T10:00:00Z"
  }
}
```

### 5.3 获取批量生成状态
**接口地址**：`GET /api/parameter/batch-status/:batchId`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| batchId | string | 是 | 批量任务ID | "batch_1642320000000_abc123" |

**请求示例**：
```bash
GET /api/parameter/batch-status/batch_1642320000000_abc123
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "batchId": "batch_1642320000000_abc123",
    "status": "completed",
    "progress": 100,
    "totalCount": 2,
    "completedCount": 2,
    "failedCount": 0,
    "results": [
      {
        "dataId": "user-data-123",
        "status": "success",
        "imageUrl": "http://localhost:7001/static/generated/user-data-123_1242x2208.jpg",
        "generatedAt": "2025-01-16T10:01:00Z"
      },
      {
        "dataId": "user-data-124",
        "status": "success",
        "imageUrl": "http://localhost:7001/static/generated/user-data-124_1242x2208.jpg",
        "generatedAt": "2025-01-16T10:01:30Z"
      }
    ],
    "createdAt": "2025-01-16T10:00:00Z",
    "completedAt": "2025-01-16T10:01:30Z"
  }
}
```

## 6. 外部API接口（主项目需要实现）

### 6.1 获取参数数据
**接口地址**：`GET /api/external/parameter-data/:dataId`

**认证**：Bearer Token

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| dataId | string | 是 | 参数数据ID | "user-data-123" |

**请求示例**：
```bash
GET /api/external/parameter-data/user-data-123
Authorization: Bearer your-api-key
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "user-data-123",
    "configId": "config-456",
    "templateId": "2",
    "parameterValues": {
      "greeting": "你好,新年快乐",
      "quote": "成功不是终点，失败不是致命的，重要的是继续前进的勇气。",
      "contact": "电话：138-0000-0000\n地址：北京市朝阳区xxx路123号"
    },
    "createdAt": "2025-01-16T10:00:00Z",
    "updatedAt": "2025-01-16T10:00:00Z"
  }
}
```

### 6.2 获取参数配置
**接口地址**：`GET /api/external/parameter-config/:configId`

**认证**：Bearer Token

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| configId | string | 是 | 配置ID | "config-456" |

**请求示例**：
```bash
GET /api/external/parameter-config/config-456
Authorization: Bearer your-api-key
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "config-456",
    "templateId": "2",
    "parameters": [
      {
        "id": "param-1",
        "elementUuid": "98fd9b16db8a",
        "parameterName": "greeting",
        "parameterLabel": "个性问候语",
        "parameterType": "text",
        "isRequired": true,
        "defaultValue": "你好,十二月",
        "validationRules": {
          "maxLength": 20
        },
        "displayOrder": 1,
        "isEnabled": true
      }
    ]
  }
}
```

### 6.3 健康检查
**接口地址**：`GET /api/external/health`

**认证**：Bearer Token

**请求示例**：
```bash
GET /api/external/health
Authorization: Bearer your-api-key
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "status": "ok",
    "timestamp": "2025-01-16T10:00:00Z",
    "version": "1.0.0",
    "uptime": 86400
  }
}
```

## 7. 错误处理

### 7.1 常见错误响应

**400 Bad Request**：
```json
{
  "code": 400,
  "message": "Invalid request parameters",
  "error": {
    "field": "templateId",
    "reason": "Template ID is required"
  }
}
```

**404 Not Found**：
```json
{
  "code": 404,
  "message": "Template not found",
  "error": {
    "templateId": "999",
    "reason": "Template with ID 999 does not exist"
  }
}
```

**500 Internal Server Error**：
```json
{
  "code": 500,
  "message": "Internal server error",
  "error": {
    "reason": "Failed to parse template data",
    "timestamp": "2025-01-16T10:00:00Z"
  }
}
```

## 8. 接口使用示例

### 8.1 完整业务流程示例

```javascript
// 1. 获取模板列表
const templates = await fetch('/api/templates?page=1&pageSize=10')
  .then(res => res.json());

// 2. 选择模板并解析
const parseResult = await fetch('/api/template/parse', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ templateId: '2' })
}).then(res => res.json());

// 3. 生成预览（需要先在主项目中配置参数和填写数据）
const preview = await fetch('/api/parameter/preview', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    templateId: '2',
    parameterDataId: 'user-data-123'
  })
}).then(res => res.json());

// 4. 生成最终图片
const image = await fetch('/api/screenshots?parameterDataId=user-data-123&width=1242&height=2208')
  .then(res => res.json());
```

---

**文档版本**: v1.0  
**创建日期**: 2025-01-16  
**API版本**: v1  
**审核状态**: 待审核
