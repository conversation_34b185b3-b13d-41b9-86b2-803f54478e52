# 动态参数模板系统API文档

## 概述

动态参数模板系统为迅排设计项目提供了完整的模板参数化功能，支持模板解析、参数替换、预览生成和批量图片生成。本文档详细描述了所有API接口的使用方法。

## 基础信息

- **基础URL**: `http://localhost:7001`
- **API版本**: v1.0.0
- **响应格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据内容
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": {
    "reason": "详细错误原因",
    "timestamp": "2025-01-16T10:00:00Z"
  }
}
```

## API接口列表

### 1. 模板数据服务

#### 1.1 获取模板列表
- **接口**: `GET /api/templates`
- **功能**: 获取模板列表，支持分页和筛选
- **参数**:
  - `page` (可选): 页码，默认1
  - `pageSize` (可选): 每页数量，默认10，最大100
  - `category` (可选): 分类筛选
  - `keyword` (可选): 关键词搜索

**请求示例**:
```bash
GET /api/templates?page=1&pageSize=5&category=poster&keyword=日签
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "2",
        "title": "示例模板 - 日签插画手机海报",
        "description": "这是一个精美的设计模板",
        "thumbnail": "http://localhost:7001/static/2-cover.jpg",
        "category": "poster",
        "tags": ["日签", "插画"],
        "width": 1242,
        "height": 2208,
        "createdAt": "2025-01-16T10:00:00Z",
        "updatedAt": "2025-01-16T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 5,
    "totalPages": 1
  }
}
```

#### 1.2 获取模板详情
- **接口**: `GET /api/template`
- **功能**: 获取指定模板的详细信息
- **参数**:
  - `id` (必填): 模板ID

**请求示例**:
```bash
GET /api/template?id=2
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "2",
    "title": "示例模板 - 日签插画手机海报",
    "description": "这是一个精美的设计模板",
    "thumbnail": "http://localhost:7001/static/2-cover.jpg",
    "category": "poster",
    "tags": ["日签", "插画"],
    "width": 1242,
    "height": 2208,
    "textElementsCount": 6,
    "createdAt": "2025-01-16T10:00:00Z",
    "updatedAt": "2025-01-16T10:00:00Z",
    "metadata": {
      "author": "设计师",
      "version": "1.0",
      "license": "免费商用"
    }
  }
}
```

#### 1.3 获取模板预览
- **接口**: `GET /api/template/preview`
- **功能**: 生成模板预览图片
- **参数**:
  - `id` (必填): 模板ID
  - `width` (可选): 预览宽度，默认400
  - `height` (可选): 预览高度，默认600
  - `quality` (可选): 图片质量，0.1-1.0，默认0.8

**请求示例**:
```bash
GET /api/template/preview?id=2&width=400&height=600&quality=0.8
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "previewUrl": "http://localhost:7001/static/previews/2_400x600.jpg",
    "width": 400,
    "height": 600,
    "fileSize": 45678,
    "generatedAt": "2025-01-16T10:00:00Z"
  }
}
```

### 2. 模板解析服务

#### 2.1 解析模板内容
- **接口**: `POST /api/template/parse`
- **功能**: 解析模板，识别文本元素并生成参数候选项
- **请求体**:
  - `templateId` (必填): 模板ID

**请求示例**:
```bash
POST /api/template/parse
Content-Type: application/json

{
  "templateId": "2"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "templateId": "2",
    "templateTitle": "示例模板 - 日签插画手机海报",
    "textElements": [
      {
        "uuid": "98fd9b16db8a",
        "type": "w-text",
        "text": "你好,十二月",
        "position": {
          "left": 84.11,
          "top": 289.4,
          "width": 1092.38,
          "height": 211
        },
        "style": {
          "fontSize": 176,
          "color": "#000000ff",
          "textAlign": "center",
          "lineHeight": 1.2,
          "letterSpacing": 10,
          "fontWeight": 400,
          "fontStyle": "normal"
        }
      }
    ],
    "parameterCandidates": [
      {
        "elementUuid": "98fd9b16db8a",
        "suggestedName": "greeting",
        "suggestedLabel": "问候语",
        "suggestedDescription": "可自定义的文本内容",
        "suggestedType": "text",
        "originalText": "你好,十二月",
        "textCategory": "general",
        "maxLength": 100,
        "isRequired": false
      }
    ],
    "summary": {
      "totalTextElements": 6,
      "totalParameterCandidates": 6,
      "categories": {
        "phone": 1,
        "general": 4,
        "url": 1
      }
    }
  }
}
```

### 3. 参数化功能

#### 3.1 生成参数化预览
- **接口**: `POST /api/parameter/preview`
- **功能**: 根据参数数据生成预览
- **请求体**:
  - `templateId` (必填): 模板ID
  - `parameterDataId` (必填): 参数数据ID

**请求示例**:
```bash
POST /api/parameter/preview
Content-Type: application/json

{
  "templateId": "2",
  "parameterDataId": "test-data-123"
}
```

#### 3.2 执行参数替换
- **接口**: `POST /api/parameter/replace`
- **功能**: 执行参数替换，返回替换后的模板数据
- **请求体**:
  - `templateId` (必填): 模板ID
  - `parameterDataId` (必填): 参数数据ID

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "templateData": "替换后的模板JSON数据",
    "replacedElements": 6,
    "errors": [],
    "metadata": {
      "templateId": "2",
      "dataId": "test-data-123",
      "processedAt": "2025-01-16T10:00:00Z",
      "replacements": [
        {
          "elementUuid": "98fd9b16db8a",
          "originalText": "你好,十二月",
          "newText": "你好,新年快乐",
          "parameterName": "greeting"
        }
      ]
    }
  }
}
```

#### 3.3 获取预览页面
- **接口**: `GET /preview/parameter/:dataId`
- **功能**: 获取参数化预览页面HTML
- **参数**:
  - `dataId` (路径参数): 参数数据ID

**请求示例**:
```bash
GET /preview/parameter/test-data-123
```

### 4. 图片生成服务

#### 4.1 生成参数化截图
- **接口**: `GET /api/screenshots`
- **功能**: 生成参数化图片（扩展现有接口）
- **参数**:
  - `parameterDataId` (可选): 参数数据ID，用于参数化截图
  - `width` (必填): 图片宽度
  - `height` (必填): 图片高度
  - `type` (可选): 返回类型，file或cover，默认file
  - `size` (可选): 按比例缩小到指定宽度
  - `quality` (可选): 图片质量，0.1-1.0

**请求示例**:
```bash
GET /api/screenshots?parameterDataId=test-data-123&width=1242&height=2208&quality=0.9
```

#### 4.2 批量生成图片
- **接口**: `POST /api/parameter/batch-generate`
- **功能**: 批量生成参数化图片
- **请求体**:
  - `dataIds` (必填): 参数数据ID数组
  - `outputOptions` (必填): 输出选项

**请求示例**:
```bash
POST /api/parameter/batch-generate
Content-Type: application/json

{
  "dataIds": ["test-data-123", "test-data-456"],
  "outputOptions": {
    "width": 1242,
    "height": 2208,
    "type": "file",
    "size": 2,
    "quality": 0.9
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "batchId": "batch_1755432640277_xxx",
    "status": "processing",
    "totalItems": 2,
    "message": "批量任务已创建，正在处理中"
  }
}
```

#### 4.3 查询批量任务状态
- **接口**: `GET /api/parameter/batch-status/:batchId`
- **功能**: 查询批量生成任务的状态和进度
- **参数**:
  - `batchId` (路径参数): 批量任务ID

**请求示例**:
```bash
GET /api/parameter/batch-status/batch_1755432640277_xxx
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "batchId": "batch_1755432640277_xxx",
    "status": "completed",
    "progress": 100,
    "totalItems": 2,
    "completedItems": 2,
    "failedItems": 0,
    "createdAt": "2025-01-16T10:00:00Z",
    "updatedAt": "2025-01-16T10:05:00Z",
    "results": [
      {
        "dataId": "test-data-123",
        "status": "success",
        "imageUrl": "http://localhost:7001/static/generated/test-data-123_1242x2208.jpg"
      }
    ],
    "outputOptions": {
      "width": 1242,
      "height": 2208,
      "type": "file",
      "quality": 0.9
    }
  }
}
```

### 5. 健康检查

#### 5.1 系统健康检查
- **接口**: `GET /health`
- **功能**: 检查系统整体健康状态

#### 5.2 外部API状态检查
- **接口**: `GET /api/health/external-api`
- **功能**: 检查外部API连接状态

#### 5.3 缓存状态查询
- **接口**: `GET /api/health/cache`
- **功能**: 查询缓存状态和统计信息

#### 5.4 清理缓存
- **接口**: `POST /api/health/cache/clear`
- **功能**: 清理所有缓存数据

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 使用限制

- API请求频率限制：每秒最多100次请求
- 批量生成：每次最多处理50个项目
- 图片生成：最大尺寸4000x4000像素
- 缓存时间：模板数据缓存10分钟，用户数据缓存5分钟

## 集成示例

### JavaScript/TypeScript 集成示例

```typescript
// API客户端封装
class DynamicTemplateAPI {
  private baseURL = 'http://localhost:7001'

  async getTemplates(params?: {
    page?: number
    pageSize?: number
    category?: string
    keyword?: string
  }) {
    const query = new URLSearchParams(params as any).toString()
    const response = await fetch(`${this.baseURL}/api/templates?${query}`)
    return response.json()
  }

  async parseTemplate(templateId: string) {
    const response = await fetch(`${this.baseURL}/api/template/parse`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ templateId })
    })
    return response.json()
  }

  async generatePreview(templateId: string, parameterDataId: string) {
    const response = await fetch(`${this.baseURL}/api/parameter/preview`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ templateId, parameterDataId })
    })
    return response.json()
  }

  async batchGenerate(dataIds: string[], outputOptions: any) {
    const response = await fetch(`${this.baseURL}/api/parameter/batch-generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ dataIds, outputOptions })
    })
    return response.json()
  }
}

// 使用示例
const api = new DynamicTemplateAPI()

// 1. 获取模板列表
const templates = await api.getTemplates({ category: 'poster', pageSize: 10 })

// 2. 解析模板
const parseResult = await api.parseTemplate('2')

// 3. 生成预览
const preview = await api.generatePreview('2', 'test-data-123')

// 4. 批量生成
const batch = await api.batchGenerate(
  ['test-data-123', 'test-data-456'],
  { width: 1242, height: 2208, quality: 0.9 }
)
```

### Vue.js 组件集成示例

```vue
<template>
  <div class="dynamic-template-system">
    <!-- 模板选择 -->
    <div class="template-selector">
      <h3>选择模板</h3>
      <div class="template-grid">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-item"
          :class="{ active: selectedTemplate?.id === template.id }"
          @click="selectTemplate(template)"
        >
          <img :src="template.thumbnail" :alt="template.title" />
          <p>{{ template.title }}</p>
        </div>
      </div>
    </div>

    <!-- 参数配置 -->
    <div v-if="selectedTemplate && parameterCandidates.length" class="parameter-config">
      <h3>参数配置</h3>
      <form @submit.prevent="generatePreview">
        <div
          v-for="param in parameterCandidates"
          :key="param.elementUuid"
          class="form-group"
        >
          <label>{{ param.suggestedLabel }}</label>
          <input
            v-if="param.suggestedType === 'text'"
            v-model="parameterValues[param.suggestedName]"
            type="text"
            :placeholder="param.originalText"
            :maxlength="param.maxLength"
            :required="param.isRequired"
          />
          <textarea
            v-else-if="param.suggestedType === 'textarea'"
            v-model="parameterValues[param.suggestedName]"
            :placeholder="param.originalText"
            :maxlength="param.maxLength"
            :required="param.isRequired"
          ></textarea>
          <input
            v-else-if="param.suggestedType === 'email'"
            v-model="parameterValues[param.suggestedName]"
            type="email"
            :placeholder="param.originalText"
            :required="param.isRequired"
          />
        </div>
        <button type="submit" :disabled="loading">生成预览</button>
      </form>
    </div>

    <!-- 预览区域 -->
    <div v-if="previewUrl" class="preview-area">
      <h3>预览效果</h3>
      <iframe :src="previewUrl" width="400" height="600"></iframe>
      <button @click="generateImage" :disabled="loading">生成图片</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { DynamicTemplateAPI } from './api'

const api = new DynamicTemplateAPI()
const templates = ref([])
const selectedTemplate = ref(null)
const parameterCandidates = ref([])
const parameterValues = ref({})
const previewUrl = ref('')
const loading = ref(false)

onMounted(async () => {
  const result = await api.getTemplates({ category: 'poster' })
  templates.value = result.data.list
})

async function selectTemplate(template: any) {
  selectedTemplate.value = template
  loading.value = true

  try {
    const parseResult = await api.parseTemplate(template.id)
    parameterCandidates.value = parseResult.data.parameterCandidates

    // 初始化参数值
    parameterValues.value = {}
    parameterCandidates.value.forEach(param => {
      parameterValues.value[param.suggestedName] = param.originalText
    })
  } finally {
    loading.value = false
  }
}

async function generatePreview() {
  if (!selectedTemplate.value) return

  loading.value = true
  try {
    // 这里需要先保存参数数据，获取dataId
    const dataId = await saveParameterData(parameterValues.value)

    const result = await api.generatePreview(selectedTemplate.value.id, dataId)
    previewUrl.value = result.data.previewUrl
  } finally {
    loading.value = false
  }
}

async function generateImage() {
  // 调用截图API生成最终图片
  const imageUrl = `${api.baseURL}/api/screenshots?parameterDataId=${dataId}&width=1242&height=2208`
  window.open(imageUrl, '_blank')
}
</script>
```
