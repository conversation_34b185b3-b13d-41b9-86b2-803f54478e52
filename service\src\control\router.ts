/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2020-07-22 20:13:14
 * @Description: 路由
 * @LastEditors: <PERSON><PERSON><PERSON> <https://m.palxp.cn>
 * @LastEditTime: 2024-08-12 13:40:13
 */
import rExpress from 'express'
import screenshots from '../service/screenshots'
import fileService from '../service/files'
import userService from '../service/user'
import designService from '../service/design'
import templateController from '../controllers/templateController'
import parserController from '../controllers/parserController'
import healthController from '../controllers/healthController'
import parameterController from '../controllers/parameterController'
import batchController from '../controllers/batchController'
import api from './api'
const rRouter = rExpress.Router()

rRouter.get(api.SCREENGHOT, screenshots.screenshots)
rRouter.get(api.PRINTSCREEN, screenshots.printscreen)
rRouter.post(api.UPLOAD, fileService.upload)
rRouter.get(api.USER_IMAGES, userService.getUserImages)
rRouter.get(api.GET_TEMPLATE_LIST, designService.getTemplates)
rRouter.get(api.GET_TEMPLATE, designService.getDetail)
rRouter.get(api.GET_MATERIAL, designService.getMaterial)
rRouter.get(api.GET_PHOTOS, designService.getPhotos)
rRouter.post(api.UPDATE_TEMPLATE, designService.saveTemplate)

// 动态参数模板系统路由
rRouter.get(api.TEMPLATES, templateController.getTemplateList)
rRouter.get(api.TEMPLATE_DETAIL, templateController.getTemplateDetail)
rRouter.get(api.TEMPLATE_PREVIEW, templateController.getTemplatePreview)
rRouter.post(api.TEMPLATE_PARSE, parserController.parseTemplate)

// 参数化功能路由
rRouter.post(api.PARAMETER_PREVIEW, parameterController.generatePreview)
rRouter.post(api.PARAMETER_REPLACE, parameterController.replaceParameters)
rRouter.get(api.PREVIEW_PARAMETER + '/:dataId', parameterController.getPreviewPage)

// 批量生成路由
rRouter.post(api.PARAMETER_BATCH_GENERATE, batchController.batchGenerate)
rRouter.get(api.PARAMETER_BATCH_STATUS + '/:batchId', batchController.getBatchStatus)
rRouter.get(api.PARAMETER_BATCH_TASKS, batchController.getAllBatchTasks)

// 健康检查路由
rRouter.get(api.HEALTH, healthController.healthCheck)
rRouter.get(api.HEALTH_EXTERNAL_API, healthController.checkExternalApiStatus)
rRouter.get(api.HEALTH_CACHE, healthController.getCacheStatus)
rRouter.post(api.HEALTH_CACHE_CLEAR, healthController.clearCache)

export default rRouter
