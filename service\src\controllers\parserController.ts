/**
 * 模板解析控制器
 * 提供模板解析和参数候选项生成API接口
 */

import { send } from '../utils/tools'
import templateParserService from '../services/templateParser'

/**
 * 解析模板内容
 * POST /api/template/parse
 */
export async function parseTemplate(req: any, res: any) {
  try {
    const { templateId } = req.body
    
    // 参数验证
    if (!templateId) {
      return send.error(res, '模板ID不能为空', 400)
    }
    
    if (typeof templateId !== 'string' || templateId.trim() === '') {
      return send.error(res, '模板ID格式无效', 400)
    }
    
    // 解析模板
    const parseResult = await templateParserService.parseTemplate(templateId.trim())
    
    send.success(res, parseResult)
  } catch (error) {
    console.error('解析模板失败:', error)
    
    if (error.message.includes('不存在')) {
      return send.error(res, '模板不存在', 404)
    }
    
    send.error(res, '解析模板失败', 500)
  }
}

export default {
  parseTemplate
}
