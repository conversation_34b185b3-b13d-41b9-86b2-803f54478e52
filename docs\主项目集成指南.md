# 主项目集成指南

## 1. 集成概述

### 1.1 集成架构
本指南帮助主项目开发者集成迅排设计的动态参数模板系统，实现模板参数化配置和个性化图片生成功能。

```
主项目 ←→ 迅排设计服务
  ↓           ↓
数据库    文件系统/缓存
```

### 1.2 职责分工
- **主项目**：参数配置管理、用户数据存储、前端界面、外部API提供
- **迅排设计服务**：模板解析、内容替换、图片生成、预览服务

### 1.3 集成流程
1. 主项目调用迅排设计API获取模板和解析结果
2. 主项目实现参数配置管理和用户表单
3. 主项目提供外部API供迅排设计调用
4. 迅排设计提供预览和图片生成服务

## 2. 数据库设计

### 2.1 数据库表结构

#### 模板参数配置表
```sql
CREATE TABLE poster_template_configs (
  id VARCHAR(32) PRIMARY KEY COMMENT '配置ID',
  template_id VARCHAR(32) NOT NULL COMMENT '迅排设计模板ID',
  template_title VARCHAR(255) COMMENT '模板标题',
  config_name VARCHAR(255) NOT NULL COMMENT '配置名称',
  config_description TEXT COMMENT '配置描述',
  parameters JSON NOT NULL COMMENT '参数定义JSON',
  created_by VARCHAR(32) COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status TINYINT DEFAULT 1 COMMENT '状态：1启用 0禁用',
  
  INDEX idx_template_id (template_id),
  INDEX idx_created_by (created_by),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板参数配置表';
```

#### 用户参数数据表
```sql
CREATE TABLE poster_user_data (
  id VARCHAR(32) PRIMARY KEY COMMENT '数据ID',
  config_id VARCHAR(32) NOT NULL COMMENT '配置ID',
  user_id VARCHAR(32) COMMENT '用户ID',
  session_id VARCHAR(64) COMMENT '会话ID（匿名用户）',
  parameter_values JSON NOT NULL COMMENT '用户填写的参数值',
  is_draft BOOLEAN DEFAULT TRUE COMMENT '是否为草稿',
  preview_url VARCHAR(500) COMMENT '预览页面URL',
  generated_image_url VARCHAR(500) COMMENT '生成的图片URL',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_config_id (config_id),
  INDEX idx_user_id (user_id),
  INDEX idx_session_id (session_id),
  INDEX idx_is_draft (is_draft),
  FOREIGN KEY (config_id) REFERENCES poster_template_configs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户参数数据表';
```

#### 图片生成记录表
```sql
CREATE TABLE poster_generation_records (
  id VARCHAR(32) PRIMARY KEY COMMENT '记录ID',
  data_id VARCHAR(32) NOT NULL COMMENT '参数数据ID',
  image_url VARCHAR(500) NOT NULL COMMENT '生成的图片URL',
  generation_options JSON COMMENT '生成选项',
  generation_time DECIMAL(10,3) COMMENT '生成耗时（秒）',
  file_size INT COMMENT '文件大小（字节）',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_data_id (data_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (data_id) REFERENCES poster_user_data(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片生成记录表';
```

### 2.2 示例数据

#### 参数配置示例
```json
{
  "id": "config-001",
  "templateId": "2",
  "configName": "个性化日签配置",
  "parameters": [
    {
      "id": "param-1",
      "elementUuid": "98fd9b16db8a",
      "parameterName": "greeting",
      "parameterLabel": "个性问候语",
      "parameterType": "text",
      "isRequired": true,
      "defaultValue": "你好,十二月",
      "validationRules": {"maxLength": 20},
      "displayOrder": 1,
      "isEnabled": true
    }
  ]
}
```

#### 用户数据示例
```json
{
  "id": "user-data-123",
  "configId": "config-001",
  "parameterValues": {
    "greeting": "你好,新年快乐",
    "quote": "成功不是终点，失败不是致命的，重要的是继续前进的勇气。",
    "contact": "电话：138-0000-0000\n地址：北京市朝阳区xxx路123号"
  }
}
```

## 3. 外部API实现

### 3.1 API认证配置

#### 环境变量配置
```bash
# API Key配置
POSTER_API_KEY=your-secure-api-key-here
POSTER_API_BASE_URL=http://localhost:7001

# 安全配置
API_RATE_LIMIT=1000
API_TIMEOUT=30000
```

#### 认证中间件
```javascript
// middleware/auth.js
function validateApiKey(req, res, next) {
  const apiKey = req.headers.authorization?.replace('Bearer ', '');
  
  if (!apiKey || apiKey !== process.env.POSTER_API_KEY) {
    return res.status(401).json({
      code: 401,
      message: 'Invalid API key'
    });
  }
  
  next();
}

module.exports = { validateApiKey };
```

### 3.2 必需的外部API接口

#### 获取参数数据接口
```javascript
// routes/external.js
const express = require('express');
const { validateApiKey } = require('../middleware/auth');
const router = express.Router();

// 获取参数数据
router.get('/parameter-data/:dataId', validateApiKey, async (req, res) => {
  try {
    const { dataId } = req.params;
    
    // 从数据库查询用户数据
    const userData = await db.query(`
      SELECT pd.*, pc.template_id
      FROM poster_user_data pd
      JOIN poster_template_configs pc ON pd.config_id = pc.id
      WHERE pd.id = ?
    `, [dataId]);
    
    if (userData.length === 0) {
      return res.status(404).json({
        code: 404,
        message: 'Parameter data not found'
      });
    }
    
    const data = userData[0];
    res.json({
      code: 200,
      data: {
        id: data.id,
        configId: data.config_id,
        templateId: data.template_id,
        parameterValues: JSON.parse(data.parameter_values)
      }
    });
  } catch (error) {
    console.error('Get parameter data error:', error);
    res.status(500).json({
      code: 500,
      message: 'Internal server error'
    });
  }
});

// 获取参数配置
router.get('/parameter-config/:configId', validateApiKey, async (req, res) => {
  try {
    const { configId } = req.params;
    
    const config = await db.query(
      'SELECT * FROM poster_template_configs WHERE id = ? AND status = 1',
      [configId]
    );
    
    if (config.length === 0) {
      return res.status(404).json({
        code: 404,
        message: 'Parameter config not found'
      });
    }
    
    const data = config[0];
    res.json({
      code: 200,
      data: {
        id: data.id,
        templateId: data.template_id,
        parameters: JSON.parse(data.parameters)
      }
    });
  } catch (error) {
    console.error('Get parameter config error:', error);
    res.status(500).json({
      code: 500,
      message: 'Internal server error'
    });
  }
});

// 健康检查
router.get('/health', validateApiKey, (req, res) => {
  res.json({
    code: 200,
    data: {
      status: 'ok',
      timestamp: new Date().toISOString()
    }
  });
});

module.exports = router;
```

## 4. 前端界面实现

### 4.1 管理员配置界面

#### 模板选择组件
```vue
<!-- components/TemplateSelector.vue -->
<template>
  <div class="template-selector">
    <el-card>
      <template #header>
        <span>选择模板</span>
      </template>
      
      <!-- 模板列表 -->
      <el-row :gutter="20">
        <el-col 
          v-for="template in templates" 
          :key="template.id" 
          :span="6"
        >
          <el-card 
            :class="{ 'selected': selectedTemplate?.id === template.id }"
            @click="selectTemplate(template)"
            class="template-card"
          >
            <img :src="template.thumbnail" :alt="template.title" />
            <div class="template-info">
              <h4>{{ template.title }}</h4>
              <p>{{ template.description }}</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        @current-change="loadTemplates"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const templates = ref([])
const selectedTemplate = ref(null)
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

const emit = defineEmits(['template-selected'])

// 加载模板列表
async function loadTemplates() {
  try {
    const response = await api.poster.getTemplates({
      page: currentPage.value,
      pageSize: pageSize.value
    })
    
    templates.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    ElMessage.error('加载模板列表失败')
  }
}

// 选择模板
function selectTemplate(template) {
  selectedTemplate.value = template
  emit('template-selected', template)
}

onMounted(() => {
  loadTemplates()
})
</script>

<style scoped>
.template-card {
  cursor: pointer;
  transition: all 0.3s;
}

.template-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.template-card.selected {
  border-color: #409eff;
}

.template-info h4 {
  margin: 10px 0 5px 0;
  font-size: 14px;
}

.template-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
}
</style>
```

#### 参数配置组件
```vue
<!-- components/ParameterConfig.vue -->
<template>
  <div class="parameter-config">
    <el-card>
      <template #header>
        <span>参数配置</span>
        <el-button 
          type="primary" 
          @click="parseTemplate"
          :loading="parsing"
        >
          解析模板
        </el-button>
      </template>
      
      <!-- 参数候选项列表 -->
      <el-table 
        v-if="parameterCandidates.length > 0"
        :data="parameterCandidates" 
        border
      >
        <el-table-column prop="originalText" label="原始文本" width="200" />
        <el-table-column label="参数标签" width="150">
          <template #default="{ row }">
            <el-input v-model="row.parameterLabel" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="参数名称" width="150">
          <template #default="{ row }">
            <el-input v-model="row.parameterName" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="参数类型" width="120">
          <template #default="{ row }">
            <el-select v-model="row.parameterType" size="small">
              <el-option label="文本" value="text" />
              <el-option label="多行文本" value="textarea" />
              <el-option label="电话" value="phone" />
              <el-option label="邮箱" value="email" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="是否开放" width="100">
          <template #default="{ row }">
            <el-switch v-model="row.isEnabled" />
          </template>
        </el-table-column>
        <el-table-column label="必填" width="80">
          <template #default="{ row }">
            <el-switch v-model="row.isRequired" />
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 操作按钮 -->
      <div class="actions" v-if="parameterCandidates.length > 0">
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
        <el-button @click="previewForm">预览表单</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const props = defineProps({
  selectedTemplate: Object
})

const parameterCandidates = ref([])
const parsing = ref(false)

// 解析模板
async function parseTemplate() {
  if (!props.selectedTemplate) {
    ElMessage.warning('请先选择模板')
    return
  }
  
  parsing.value = true
  try {
    const response = await api.poster.parseTemplate({
      templateId: props.selectedTemplate.id
    })
    
    parameterCandidates.value = response.data.parameterCandidates.map(candidate => ({
      ...candidate,
      parameterLabel: candidate.suggestedLabel,
      parameterName: candidate.suggestedName,
      parameterType: candidate.suggestedType,
      isEnabled: true,
      isRequired: false
    }))
    
    ElMessage.success('模板解析成功')
  } catch (error) {
    ElMessage.error('模板解析失败')
  } finally {
    parsing.value = false
  }
}

// 保存配置
async function saveConfig() {
  try {
    const enabledParameters = parameterCandidates.value.filter(p => p.isEnabled)
    
    const configData = {
      templateId: props.selectedTemplate.id,
      templateTitle: props.selectedTemplate.title,
      configName: `配置_${Date.now()}`,
      parameters: enabledParameters
    }
    
    await api.parameter.saveConfig(configData)
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('配置保存失败')
  }
}

// 预览表单
function previewForm() {
  // 实现表单预览逻辑
  const enabledParameters = parameterCandidates.value.filter(p => p.isEnabled)
  console.log('预览表单参数:', enabledParameters)
}
</script>

<style scoped>
.actions {
  margin-top: 20px;
  text-align: center;
}
</style>
```

### 4.2 用户表单组件

#### 动态表单生成器
```vue
<!-- components/DynamicForm.vue -->
<template>
  <div class="dynamic-form">
    <el-form 
      ref="formRef"
      :model="formData" 
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item
        v-for="param in parameters"
        :key="param.id"
        :label="param.parameterLabel"
        :prop="param.parameterName"
      >
        <!-- 文本输入 -->
        <el-input
          v-if="param.parameterType === 'text'"
          v-model="formData[param.parameterName]"
          :placeholder="param.parameterDescription"
          :maxlength="param.validationRules?.maxLength"
        />
        
        <!-- 多行文本 -->
        <el-input
          v-else-if="param.parameterType === 'textarea'"
          v-model="formData[param.parameterName]"
          type="textarea"
          :rows="3"
          :placeholder="param.parameterDescription"
          :maxlength="param.validationRules?.maxLength"
        />
        
        <!-- 电话号码 -->
        <el-input
          v-else-if="param.parameterType === 'phone'"
          v-model="formData[param.parameterName]"
          :placeholder="param.parameterDescription"
        />
        
        <!-- 邮箱 -->
        <el-input
          v-else-if="param.parameterType === 'email'"
          v-model="formData[param.parameterName]"
          :placeholder="param.parameterDescription"
        />
        
        <div class="form-help" v-if="param.parameterDescription">
          {{ param.parameterDescription }}
        </div>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">预览效果</el-button>
        <el-button @click="generateImage" :loading="generating">生成图片</el-button>
        <el-button @click="saveDraft">保存草稿</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const props = defineProps({
  configId: String
})

const formRef = ref()
const formData = reactive({})
const parameters = ref([])
const generating = ref(false)

// 动态生成表单验证规则
const formRules = computed(() => {
  const rules = {}
  parameters.value.forEach(param => {
    if (param.isRequired) {
      rules[param.parameterName] = [
        { required: true, message: `请输入${param.parameterLabel}`, trigger: 'blur' }
      ]
    }
    
    // 添加其他验证规则
    if (param.validationRules?.maxLength) {
      rules[param.parameterName] = rules[param.parameterName] || []
      rules[param.parameterName].push({
        max: param.validationRules.maxLength,
        message: `长度不能超过${param.validationRules.maxLength}个字符`,
        trigger: 'blur'
      })
    }
  })
  return rules
})

// 加载参数配置
async function loadConfig() {
  try {
    const response = await api.parameter.getConfig(props.configId)
    parameters.value = response.data.parameters
    
    // 初始化表单数据
    parameters.value.forEach(param => {
      formData[param.parameterName] = param.defaultValue || ''
    })
  } catch (error) {
    ElMessage.error('加载配置失败')
  }
}

// 提交表单（预览）
async function submitForm() {
  try {
    await formRef.value.validate()
    
    // 保存用户数据
    const response = await api.parameter.saveUserData({
      configId: props.configId,
      parameterValues: formData,
      isDraft: false
    })
    
    // 生成预览
    const previewResponse = await api.poster.generatePreview({
      templateId: response.data.templateId,
      parameterDataId: response.data.id
    })
    
    // 打开预览页面
    window.open(previewResponse.data.previewUrl, '_blank')
  } catch (error) {
    ElMessage.error('预览生成失败')
  }
}

// 生成图片
async function generateImage() {
  try {
    await formRef.value.validate()
    generating.value = true
    
    // 保存用户数据
    const response = await api.parameter.saveUserData({
      configId: props.configId,
      parameterValues: formData,
      isDraft: false
    })
    
    // 生成图片
    const imageResponse = await api.poster.generateImage({
      parameterDataId: response.data.id,
      width: 1242,
      height: 2208,
      quality: 0.9
    })
    
    ElMessage.success('图片生成成功')
    
    // 下载图片或显示图片URL
    window.open(imageResponse.data.url, '_blank')
  } catch (error) {
    ElMessage.error('图片生成失败')
  } finally {
    generating.value = false
  }
}

// 保存草稿
async function saveDraft() {
  try {
    await api.parameter.saveUserData({
      configId: props.configId,
      parameterValues: formData,
      isDraft: true
    })
    
    ElMessage.success('草稿保存成功')
  } catch (error) {
    ElMessage.error('草稿保存失败')
  }
}

onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.form-help {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
```

## 5. API调用示例

### 5.1 Node.js SDK示例

#### 创建API客户端
```javascript
// utils/posterApi.js
const axios = require('axios');

class PosterApiClient {
  constructor(baseURL = 'http://localhost:7001') {
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      config => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      error => Promise.reject(error)
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      response => {
        if (response.data.code !== 200) {
          throw new Error(response.data.message || 'API Error');
        }
        return response.data;
      },
      error => {
        console.error('API Error:', error.message);
        return Promise.reject(error);
      }
    );
  }

  // 获取模板列表
  async getTemplates(params = {}) {
    return this.client.get('/api/templates', { params });
  }

  // 获取模板详情
  async getTemplate(templateId) {
    return this.client.get(`/api/template/${templateId}`);
  }

  // 解析模板
  async parseTemplate(templateId) {
    return this.client.post('/api/template/parse', { templateId });
  }

  // 生成预览
  async generatePreview(templateId, parameterDataId) {
    return this.client.post('/api/parameter/preview', {
      templateId,
      parameterDataId
    });
  }

  // 生成图片
  async generateImage(params) {
    const queryString = new URLSearchParams(params).toString();
    return this.client.get(`/api/screenshots?${queryString}`);
  }

  // 批量生成
  async batchGenerate(dataIds, outputOptions) {
    return this.client.post('/api/parameter/batch-generate', {
      dataIds,
      outputOptions
    });
  }

  // 获取批量状态
  async getBatchStatus(batchId) {
    return this.client.get(`/api/parameter/batch-status/${batchId}`);
  }
}

module.exports = PosterApiClient;
```

#### 使用示例
```javascript
// services/posterService.js
const PosterApiClient = require('../utils/posterApi');
const posterApi = new PosterApiClient();

class PosterService {
  // 获取并缓存模板列表
  async getTemplateList(page = 1, pageSize = 12) {
    try {
      const response = await posterApi.getTemplates({ page, pageSize });
      return response.data;
    } catch (error) {
      console.error('获取模板列表失败:', error);
      throw error;
    }
  }

  // 解析模板并创建配置
  async createTemplateConfig(templateId, configName, userId) {
    try {
      // 1. 解析模板
      const parseResult = await posterApi.parseTemplate(templateId);

      // 2. 创建配置记录
      const configId = await this.saveTemplateConfig({
        templateId,
        templateTitle: parseResult.data.templateTitle,
        configName,
        parameters: parseResult.data.parameterCandidates,
        createdBy: userId
      });

      return {
        configId,
        parameterCandidates: parseResult.data.parameterCandidates
      };
    } catch (error) {
      console.error('创建模板配置失败:', error);
      throw error;
    }
  }

  // 生成用户个性化图片
  async generateUserImage(configId, parameterValues, userId) {
    try {
      // 1. 保存用户数据
      const userData = await this.saveUserData({
        configId,
        userId,
        parameterValues,
        isDraft: false
      });

      // 2. 生成图片
      const imageResult = await posterApi.generateImage({
        parameterDataId: userData.id,
        width: 1242,
        height: 2208,
        quality: 0.9
      });

      // 3. 记录生成历史
      await this.saveGenerationRecord({
        dataId: userData.id,
        imageUrl: imageResult.data.url,
        generationOptions: {
          width: 1242,
          height: 2208,
          quality: 0.9
        }
      });

      return imageResult.data;
    } catch (error) {
      console.error('生成用户图片失败:', error);
      throw error;
    }
  }

  // 保存模板配置到数据库
  async saveTemplateConfig(configData) {
    const configId = generateId();
    await db.query(`
      INSERT INTO poster_template_configs
      (id, template_id, template_title, config_name, parameters, created_by)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      configId,
      configData.templateId,
      configData.templateTitle,
      configData.configName,
      JSON.stringify(configData.parameters),
      configData.createdBy
    ]);
    return configId;
  }

  // 保存用户数据到数据库
  async saveUserData(userData) {
    const dataId = generateId();
    await db.query(`
      INSERT INTO poster_user_data
      (id, config_id, user_id, parameter_values, is_draft)
      VALUES (?, ?, ?, ?, ?)
    `, [
      dataId,
      userData.configId,
      userData.userId,
      JSON.stringify(userData.parameterValues),
      userData.isDraft
    ]);
    return { id: dataId };
  }

  // 保存生成记录
  async saveGenerationRecord(recordData) {
    const recordId = generateId();
    await db.query(`
      INSERT INTO poster_generation_records
      (id, data_id, image_url, generation_options)
      VALUES (?, ?, ?, ?)
    `, [
      recordId,
      recordData.dataId,
      recordData.imageUrl,
      JSON.stringify(recordData.generationOptions)
    ]);
    return recordId;
  }
}

// 生成唯一ID的工具函数
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

module.exports = PosterService;
```

### 5.2 前端API调用示例

#### API封装
```javascript
// api/poster.js
import axios from 'axios'

const posterApi = axios.create({
  baseURL: process.env.VUE_APP_POSTER_API_URL || 'http://localhost:7001',
  timeout: 30000
})

// 响应拦截器
posterApi.interceptors.response.use(
  response => {
    if (response.data.code !== 200) {
      throw new Error(response.data.message || 'API Error')
    }
    return response.data
  },
  error => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

export default {
  // 模板相关
  getTemplates: (params) => posterApi.get('/api/templates', { params }),
  getTemplate: (id) => posterApi.get(`/api/template/${id}`),
  parseTemplate: (data) => posterApi.post('/api/template/parse', data),

  // 预览相关
  generatePreview: (data) => posterApi.post('/api/parameter/preview', data),

  // 图片生成
  generateImage: (params) => posterApi.get('/api/screenshots', { params }),
  batchGenerate: (data) => posterApi.post('/api/parameter/batch-generate', data),
  getBatchStatus: (batchId) => posterApi.get(`/api/parameter/batch-status/${batchId}`)
}
```

## 6. 部署配置

### 6.1 环境配置

#### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 主项目服务
  main-project:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://user:password@mysql:3306/main_db
      - POSTER_API_URL=http://poster-service:7001
      - POSTER_API_KEY=your-secure-api-key
    depends_on:
      - mysql
      - poster-service
    networks:
      - app-network

  # 迅排设计服务
  poster-service:
    image: heimanba/poster-api:latest
    ports:
      - "7001:7001"
    environment:
      - NODE_ENV=production
      - EXTERNAL_API_URL=http://main-project:3000/api
      - EXTERNAL_API_KEY=your-secure-api-key
      - PARAMETER_CACHE_ENABLED=true
    volumes:
      - poster-cache:/cache
    networks:
      - app-network

  # 数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=main_db
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - app-network

volumes:
  mysql-data:
  poster-cache:

networks:
  app-network:
    driver: bridge
```

#### Nginx配置
```nginx
# nginx.conf
upstream main-project {
    server main-project:3000;
}

upstream poster-service {
    server poster-service:7001;
}

server {
    listen 80;
    server_name your-domain.com;

    # 主项目路由
    location / {
        proxy_pass http://main-project;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 迅排设计API路由
    location /poster-api/ {
        rewrite ^/poster-api/(.*) /$1 break;
        proxy_pass http://poster-service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

        # 增加超时时间（图片生成可能需要较长时间）
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

### 6.2 监控和日志

#### 应用监控配置
```javascript
// monitoring/health.js
const express = require('express');
const PosterApiClient = require('../utils/posterApi');

const router = express.Router();
const posterApi = new PosterApiClient();

// 健康检查端点
router.get('/health', async (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {}
  };

  try {
    // 检查数据库连接
    await db.query('SELECT 1');
    health.services.database = 'ok';
  } catch (error) {
    health.services.database = 'error';
    health.status = 'error';
  }

  try {
    // 检查迅排设计服务
    await posterApi.getTemplates({ page: 1, pageSize: 1 });
    health.services.posterService = 'ok';
  } catch (error) {
    health.services.posterService = 'error';
    health.status = 'error';
  }

  const statusCode = health.status === 'ok' ? 200 : 503;
  res.status(statusCode).json(health);
});

module.exports = router;
```

## 7. 常见问题和解决方案

### 7.1 API调用问题

**问题1**: API调用超时
```javascript
// 解决方案：增加超时时间和重试机制
const posterApi = axios.create({
  timeout: 60000, // 增加到60秒
  retry: 3,
  retryDelay: 1000
});

posterApi.interceptors.response.use(null, async (error) => {
  const config = error.config;

  if (!config || !config.retry) return Promise.reject(error);

  config.__retryCount = config.__retryCount || 0;

  if (config.__retryCount >= config.retry) {
    return Promise.reject(error);
  }

  config.__retryCount += 1;

  await new Promise(resolve => setTimeout(resolve, config.retryDelay));

  return posterApi(config);
});
```

**问题2**: 图片生成失败
```javascript
// 解决方案：添加错误处理和降级方案
async function generateImageWithFallback(params) {
  try {
    return await posterApi.generateImage(params);
  } catch (error) {
    console.error('图片生成失败:', error);

    // 降级方案：返回模板预览图
    if (params.templateId) {
      return await posterApi.getTemplate(params.templateId);
    }

    throw error;
  }
}
```

### 7.2 性能优化

**缓存策略**:
```javascript
// utils/cache.js
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 600 }); // 10分钟缓存

// 缓存模板列表
async function getCachedTemplates(page, pageSize) {
  const cacheKey = `templates_${page}_${pageSize}`;
  let templates = cache.get(cacheKey);

  if (!templates) {
    templates = await posterApi.getTemplates({ page, pageSize });
    cache.set(cacheKey, templates);
  }

  return templates;
}

// 缓存解析结果
async function getCachedParseResult(templateId) {
  const cacheKey = `parse_${templateId}`;
  let result = cache.get(cacheKey);

  if (!result) {
    result = await posterApi.parseTemplate(templateId);
    cache.set(cacheKey, result, 3600); // 1小时缓存
  }

  return result;
}
```

### 7.3 错误处理最佳实践

```javascript
// utils/errorHandler.js
class ApiError extends Error {
  constructor(message, code, details) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.details = details;
  }
}

// 统一错误处理中间件
function errorHandler(error, req, res, next) {
  console.error('Error:', error);

  if (error instanceof ApiError) {
    return res.status(error.code || 500).json({
      code: error.code || 500,
      message: error.message,
      details: error.details
    });
  }

  // 默认错误响应
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    timestamp: new Date().toISOString()
  });
}

module.exports = { ApiError, errorHandler };
```

---

**文档版本**: v1.0
**创建日期**: 2025-01-16
**适用版本**: 迅排设计 v1.0+
**审核状态**: 待审核
