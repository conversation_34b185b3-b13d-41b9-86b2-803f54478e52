/**
 * 模板数据服务控制器
 * 提供模板列表、详情、预览等API接口
 */

import fs from 'fs'
import path from 'path'
import { send } from '../utils/tools'

/**
 * 获取模板列表
 * GET /api/templates
 */
export async function getTemplateList(req: any, res: any) {
  try {
    const { page = 1, pageSize = 10, category, keyword } = req.query

    // 读取模板列表数据
    const listPath = path.resolve(__dirname, '../mock/templates/list.json')
    const listData = fs.readFileSync(listPath, 'utf8')
    let templates = JSON.parse(listData)

    // 分类筛选
    if (category) {
      templates = templates.filter((template: any) => 
        template.category === category || template.tags?.includes(category)
      )
    }

    // 关键词搜索
    if (keyword) {
      const searchKeyword = String(keyword).toLowerCase()
      templates = templates.filter((template: any) => 
        template.title?.toLowerCase().includes(searchKeyword) ||
        template.description?.toLowerCase().includes(searchKeyword) ||
        template.tags?.some((tag: string) => tag.toLowerCase().includes(searchKeyword))
      )
    }

    // 分页处理
    const pageNum = Math.max(1, parseInt(String(page)))
    const pageSizeNum = Math.max(1, Math.min(100, parseInt(String(pageSize))))
    const total = templates.length
    const totalPages = Math.ceil(total / pageSizeNum)
    const startIndex = (pageNum - 1) * pageSizeNum
    const endIndex = startIndex + pageSizeNum
    const list = templates.slice(startIndex, endIndex)

    // 格式化返回数据
    const formattedList = list.map((template: any) => ({
      id: template.id,
      title: template.title,
      description: template.description || '',
      thumbnail: template.cover || `http://localhost:7001/static/${template.id}-cover.jpg`,
      category: template.category || 'poster',
      tags: template.tags || [],
      width: template.width,
      height: template.height,
      createdAt: template.createdAt || new Date().toISOString(),
      updatedAt: template.updatedAt || new Date().toISOString()
    }))

    send.success(res, {
      list: formattedList,
      total,
      page: pageNum,
      pageSize: pageSizeNum,
      totalPages
    })
  } catch (error) {
    console.error('获取模板列表失败:', error)
    send.error(res, '获取模板列表失败', 500)
  }
}

/**
 * 获取模板详情
 * GET /api/template?id=templateId
 */
export async function getTemplateDetail(req: any, res: any) {
  try {
    const { id: templateId } = req.query

    if (!templateId) {
      return send.error(res, '模板ID不能为空', 400)
    }

    // 读取模板详情数据
    const detailPath = path.resolve(__dirname, `../mock/templates/${templateId}.json`)
    
    if (!fs.existsSync(detailPath)) {
      return send.error(res, '模板不存在', 404)
    }

    const detailData = fs.readFileSync(detailPath, 'utf8')
    const template = JSON.parse(detailData)

    // 读取模板列表获取基本信息
    const listPath = path.resolve(__dirname, '../mock/templates/list.json')
    const listData = fs.readFileSync(listPath, 'utf8')
    const templates = JSON.parse(listData)
    const templateInfo = templates.find((t: any) => t.id === templateId)

    // 统计文本元素数量
    let textElementsCount = 0
    try {
      const templateData = JSON.parse(template.data)
      if (templateData.widgets) {
        textElementsCount = templateData.widgets.filter((widget: any) => widget.type === 'w-text').length
      }
    } catch (e) {
      console.warn('解析模板数据失败:', e)
    }

    // 格式化返回数据
    const formattedTemplate = {
      id: template.id,
      title: template.title,
      description: templateInfo?.description || '这是一个精美的设计模板',
      thumbnail: templateInfo?.cover || `http://localhost:7001/static/${templateId}-cover.jpg`,
      category: templateInfo?.category || 'poster',
      tags: templateInfo?.tags || [],
      width: template.width,
      height: template.height,
      textElementsCount,
      createdAt: templateInfo?.createdAt || new Date().toISOString(),
      updatedAt: templateInfo?.updatedAt || new Date().toISOString(),
      metadata: {
        author: templateInfo?.author || '设计师',
        version: templateInfo?.version || '1.0',
        license: templateInfo?.license || '免费商用'
      }
    }

    send.success(res, formattedTemplate)
  } catch (error) {
    console.error('获取模板详情失败:', error)
    send.error(res, '获取模板详情失败', 500)
  }
}

/**
 * 获取模板预览
 * GET /api/template/preview?id=templateId
 */
export async function getTemplatePreview(req: any, res: any) {
  try {
    const { id: templateId } = req.query
    const { width, height, quality = 0.8 } = req.query

    if (!templateId) {
      return send.error(res, '模板ID不能为空', 400)
    }

    // 检查模板是否存在
    const detailPath = path.resolve(__dirname, `../mock/templates/${templateId}.json`)
    if (!fs.existsSync(detailPath)) {
      return send.error(res, '模板不存在', 404)
    }

    // 读取模板信息获取默认尺寸
    const detailData = fs.readFileSync(detailPath, 'utf8')
    const template = JSON.parse(detailData)

    // 设置预览尺寸
    const previewWidth = width ? parseInt(String(width)) : Math.min(400, template.width)
    const previewHeight = height ? parseInt(String(height)) : Math.min(600, template.height)
    const previewQuality = Math.max(0.1, Math.min(1, parseFloat(String(quality))))

    // 生成预览图片URL
    const previewUrl = `http://localhost:7001/static/previews/${templateId}_${previewWidth}x${previewHeight}.jpg`

    // 这里可以调用截图服务生成预览图片
    // 为了简化，先返回预设的URL
    
    send.success(res, {
      previewUrl,
      width: previewWidth,
      height: previewHeight,
      fileSize: 45678, // 模拟文件大小
      generatedAt: new Date().toISOString()
    })
  } catch (error) {
    console.error('获取模板预览失败:', error)
    send.error(res, '获取模板预览失败', 500)
  }
}

export default {
  getTemplateList,
  getTemplateDetail,
  getTemplatePreview
}
