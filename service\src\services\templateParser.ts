/**
 * 模板解析服务
 * 解析模板JSON数据结构，识别文本元素并生成参数候选项
 */

import fs from 'fs'
import path from 'path'

/**
 * 文本内容分类
 */
export enum TextCategory {
  GENERAL = 'general',
  PHONE = 'phone',
  EMAIL = 'email',
  ADDRESS = 'address',
  DATE = 'date',
  URL = 'url'
}

/**
 * 参数类型
 */
export enum ParameterType {
  TEXT = 'text',
  TEXTAREA = 'textarea',
  EMAIL = 'email',
  PHONE = 'phone',
  DATE = 'date',
  URL = 'url'
}

/**
 * 文本元素接口
 */
export interface TextElement {
  uuid: string
  type: string
  text: string
  position: {
    left: number
    top: number
    width: number
    height: number
  }
  style: {
    fontSize: number
    color: string
    fontFamily?: string
    textAlign?: string
    lineHeight?: number
    letterSpacing?: number
    fontWeight?: string
    fontStyle?: string
  }
}

/**
 * 参数候选项接口
 */
export interface ParameterCandidate {
  elementUuid: string
  suggestedName: string
  suggestedLabel: string
  suggestedDescription: string
  suggestedType: ParameterType
  originalText: string
  textCategory: TextCategory
  maxLength: number
  isRequired: boolean
}

/**
 * 模板解析结果接口
 */
export interface ParseResult {
  templateId: string
  templateTitle: string
  textElements: TextElement[]
  parameterCandidates: ParameterCandidate[]
  summary: {
    totalTextElements: number
    totalParameterCandidates: number
    categories: Record<string, number>
  }
}

/**
 * 模板解析服务类
 */
export class TemplateParserService {
  
  /**
   * 解析模板
   */
  async parseTemplate(templateId: string): Promise<ParseResult> {
    try {
      // 读取模板数据
      const templateData = await this.loadTemplateData(templateId)
      
      // 解析文本元素
      const textElements = this.extractTextElements(templateData.data)
      
      // 生成参数候选项
      const parameterCandidates = this.generateParameterCandidates(textElements)
      
      // 生成统计信息
      const summary = this.generateSummary(textElements, parameterCandidates)
      
      return {
        templateId,
        templateTitle: templateData.title,
        textElements,
        parameterCandidates,
        summary
      }
    } catch (error) {
      throw new Error(`解析模板失败: ${error.message}`)
    }
  }
  
  /**
   * 加载模板数据
   */
  private async loadTemplateData(templateId: string): Promise<any> {
    const templatePath = path.resolve(__dirname, `../mock/templates/${templateId}.json`)
    
    if (!fs.existsSync(templatePath)) {
      throw new Error(`模板 ${templateId} 不存在`)
    }
    
    const templateContent = fs.readFileSync(templatePath, 'utf8')
    return JSON.parse(templateContent)
  }
  
  /**
   * 提取文本元素
   */
  private extractTextElements(templateDataStr: string): TextElement[] {
    try {
      const templateData = JSON.parse(templateDataStr)
      const textElements: TextElement[] = []

      // 处理多页面格式
      if (templateData.page && templateData.widgets) {
        // 单页面格式
        this.extractFromWidgets(templateData.widgets, textElements)
      } else if (Array.isArray(templateData)) {
        // 多页面格式，取第一页
        const firstPage = templateData[0]
        if (firstPage && firstPage.widgets) {
          this.extractFromWidgets(firstPage.widgets, textElements)
        } else if (firstPage && firstPage.layers) {
          // 新格式：使用layers而不是widgets
          this.extractFromWidgets(firstPage.layers, textElements)
        }
      }

      return textElements
    } catch (error) {
      console.warn('解析模板数据失败:', error)
      return []
    }
  }
  
  /**
   * 从widgets中提取文本元素
   */
  private extractFromWidgets(widgets: any[], textElements: TextElement[]): void {
    for (const widget of widgets) {
      if (widget.type === 'w-text' && widget.text) {
        textElements.push({
          uuid: widget.uuid,
          type: widget.type,
          text: widget.text,
          position: {
            left: widget.left || 0,
            top: widget.top || 0,
            width: widget.width || 0,
            height: widget.height || 0
          },
          style: {
            fontSize: widget.fontSize || 14,
            color: widget.color || '#000000',
            fontFamily: widget.fontFamily,
            textAlign: widget.textAlign,
            lineHeight: widget.lineHeight,
            letterSpacing: widget.letterSpacing,
            fontWeight: widget.fontWeight,
            fontStyle: widget.fontStyle
          }
        })
      }
    }
  }
  
  /**
   * 生成参数候选项
   */
  private generateParameterCandidates(textElements: TextElement[]): ParameterCandidate[] {
    return textElements.map(element => {
      const textCategory = this.analyzeTextCategory(element.text)
      const suggestedType = this.getParameterType(textCategory, element.text)
      const suggestedName = this.generateParameterName(element.text, textCategory)
      const suggestedLabel = this.generateParameterLabel(element.text, textCategory)
      const suggestedDescription = this.generateParameterDescription(textCategory)
      const maxLength = this.calculateMaxLength(element.text, textCategory)
      
      return {
        elementUuid: element.uuid,
        suggestedName,
        suggestedLabel,
        suggestedDescription,
        suggestedType,
        originalText: element.text,
        textCategory,
        maxLength,
        isRequired: textCategory !== TextCategory.GENERAL
      }
    })
  }
  
  /**
   * 分析文本内容类型
   */
  private analyzeTextCategory(text: string): TextCategory {
    // 清理HTML标签
    const cleanText = text.replace(/<[^>]*>/g, '')
    
    // 电话号码检测
    if (/电话|手机|tel|phone/i.test(cleanText) || /\d{3,4}[-\s]?\d{7,8}|\d{11}/.test(cleanText)) {
      return TextCategory.PHONE
    }
    
    // 邮箱检测
    if (/邮箱|email|mail/i.test(cleanText) || /\w+@\w+\.\w+/.test(cleanText)) {
      return TextCategory.EMAIL
    }
    
    // 地址检测
    if (/地址|address|addr/i.test(cleanText) || /[省市区县街道路号]/g.test(cleanText)) {
      return TextCategory.ADDRESS
    }
    
    // 日期检测
    if (/日期|时间|date|time/i.test(cleanText) || /\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?/.test(cleanText)) {
      return TextCategory.DATE
    }
    
    // URL检测
    if (/网址|链接|url|link/i.test(cleanText) || /https?:\/\//.test(cleanText)) {
      return TextCategory.URL
    }
    
    return TextCategory.GENERAL
  }
  
  /**
   * 获取参数类型
   */
  private getParameterType(category: TextCategory, text: string): ParameterType {
    switch (category) {
      case TextCategory.EMAIL:
        return ParameterType.EMAIL
      case TextCategory.PHONE:
        return ParameterType.PHONE
      case TextCategory.DATE:
        return ParameterType.DATE
      case TextCategory.URL:
        return ParameterType.URL
      default:
        // 根据文本长度判断是否使用textarea
        return text.length > 50 || text.includes('<br') ? ParameterType.TEXTAREA : ParameterType.TEXT
    }
  }
  
  /**
   * 生成参数名称
   */
  private generateParameterName(text: string, category: TextCategory): string {
    switch (category) {
      case TextCategory.PHONE:
        return 'phone'
      case TextCategory.EMAIL:
        return 'email'
      case TextCategory.ADDRESS:
        return 'address'
      case TextCategory.DATE:
        return 'date'
      case TextCategory.URL:
        return 'url'
      default:
        // 根据文本内容生成名称
        if (text.includes('标题') || text.includes('题目')) return 'title'
        if (text.includes('名称') || text.includes('姓名')) return 'name'
        if (text.includes('问候') || text.includes('你好')) return 'greeting'
        if (text.includes('引言') || text.includes('名言')) return 'quote'
        return 'text_content'
    }
  }
  
  /**
   * 生成参数标签
   */
  private generateParameterLabel(text: string, category: TextCategory): string {
    switch (category) {
      case TextCategory.PHONE:
        return '联系电话'
      case TextCategory.EMAIL:
        return '邮箱地址'
      case TextCategory.ADDRESS:
        return '联系地址'
      case TextCategory.DATE:
        return '日期时间'
      case TextCategory.URL:
        return '网址链接'
      default:
        if (text.includes('标题') || text.includes('题目')) return '标题'
        if (text.includes('名称') || text.includes('姓名')) return '姓名'
        if (text.includes('问候') || text.includes('你好')) return '问候语'
        if (text.includes('引言') || text.includes('名言')) return '名言警句'
        return '文本内容'
    }
  }
  
  /**
   * 生成参数描述
   */
  private generateParameterDescription(category: TextCategory): string {
    switch (category) {
      case TextCategory.PHONE:
        return '联系电话号码'
      case TextCategory.EMAIL:
        return '联系邮箱地址'
      case TextCategory.ADDRESS:
        return '详细的联系地址'
      case TextCategory.DATE:
        return '日期时间信息'
      case TextCategory.URL:
        return '网址链接地址'
      default:
        return '可自定义的文本内容'
    }
  }
  
  /**
   * 计算最大长度
   */
  private calculateMaxLength(text: string, category: TextCategory): number {
    switch (category) {
      case TextCategory.PHONE:
        return 50
      case TextCategory.EMAIL:
        return 100
      case TextCategory.ADDRESS:
        return 200
      case TextCategory.DATE:
        return 50
      case TextCategory.URL:
        return 200
      default:
        return Math.max(text.length * 2, 100)
    }
  }
  
  /**
   * 生成统计信息
   */
  private generateSummary(textElements: TextElement[], parameterCandidates: ParameterCandidate[]): any {
    const categories: Record<string, number> = {}
    
    parameterCandidates.forEach(candidate => {
      categories[candidate.textCategory] = (categories[candidate.textCategory] || 0) + 1
    })
    
    return {
      totalTextElements: textElements.length,
      totalParameterCandidates: parameterCandidates.length,
      categories
    }
  }
}

export default new TemplateParserService()
