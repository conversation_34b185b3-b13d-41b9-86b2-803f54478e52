# 动态参数模板系统测试文档

## 1. 测试概述

### 1.1 测试目标
确保动态参数模板系统的功能完整性、性能稳定性和接口可靠性，验证系统能够满足设计要求和用户需求。

### 1.2 测试范围
- API接口功能测试
- 模板解析准确性测试
- 参数替换正确性测试
- 图片生成质量测试
- 系统性能测试
- 集成测试
- 安全性测试

### 1.3 测试环境
- **开发环境**：http://localhost:7001
- **测试环境**：独立测试服务器
- **测试数据**：基于模板ID=2的标准测试数据

## 2. API接口测试

### 2.1 模板数据服务测试

#### 2.1.1 模板列表接口测试
**接口**：`GET /api/templates`

**测试用例T-API-001**：获取模板列表
```bash
# 请求
GET /api/templates?page=1&pageSize=10

# 预期响应
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": "1",
        "title": "模板标题",
        "thumbnail": "缩略图URL",
        "category": "分类",
        "createdAt": "创建时间"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

**验证点**：
- [ ] 响应状态码为200
- [ ] 返回数据结构正确
- [ ] 分页参数生效
- [ ] 数据字段完整

**测试用例T-API-002**：分页参数测试
```bash
# 测试不同分页参数
GET /api/templates?page=2&pageSize=5
GET /api/templates?page=1&pageSize=20
```

**验证点**：
- [ ] 分页逻辑正确
- [ ] 边界值处理正确
- [ ] 无效参数处理

#### 2.1.2 模板详情接口测试
**接口**：`GET /api/template/:templateId`

**测试用例T-API-003**：获取模板详情
```bash
# 请求
GET /api/template/2

# 预期响应
{
  "code": 200,
  "data": {
    "id": "2",
    "title": "示例模板 - 日签插画手机海报",
    "description": "模板描述",
    "thumbnail": "缩略图URL",
    "category": "分类",
    "tags": ["标签1", "标签2"],
    "createdAt": "创建时间",
    "updatedAt": "更新时间"
  }
}
```

**验证点**：
- [ ] 能够正确获取指定模板信息
- [ ] 模板不存在时返回404
- [ ] 数据字段完整准确

#### 2.1.3 模板预览接口测试
**接口**：`GET /api/template/:templateId/preview`

**测试用例T-API-004**：获取模板预览
```bash
# 请求
GET /api/template/2/preview

# 预期响应
{
  "code": 200,
  "data": {
    "previewUrl": "预览图片URL",
    "width": 1242,
    "height": 2208
  }
}
```

**验证点**：
- [ ] 能够生成模板预览图
- [ ] 预览图片可正常访问
- [ ] 图片尺寸信息正确

### 2.2 模板解析服务测试

#### 2.2.1 模板解析接口测试
**接口**：`POST /api/template/parse`

**测试用例T-API-005**：解析模板ID=2
```bash
# 请求
POST /api/template/parse
Content-Type: application/json
{
  "templateId": "2"
}

# 预期响应
{
  "code": 200,
  "data": {
    "templateId": "2",
    "templateTitle": "示例模板 - 日签插画手机海报",
    "textElements": [
      {
        "uuid": "98fd9b16db8a",
        "type": "w-text",
        "text": "你好,十二月",
        "position": {"left": 84.11, "top": 289.4, "width": 1092.38, "height": 211},
        "style": {"fontSize": 176, "color": "#000000ff", "textAlign": "center"}
      }
    ],
    "parameterCandidates": [
      {
        "elementUuid": "98fd9b16db8a",
        "suggestedName": "greeting_text",
        "suggestedLabel": "问候语",
        "suggestedType": "text",
        "originalText": "你好,十二月",
        "textCategory": "general"
      }
    ]
  }
}
```

**验证点**：
- [ ] 能够识别出5个文本元素
- [ ] 参数候选项数量正确
- [ ] 文本分类准确
- [ ] 参数名称建议合理

**测试用例T-API-006**：文本内容分类测试
```bash
# 验证电话号码识别
原文本: "电话：8888-8888888"
预期分类: "phone"
预期参数名: "phone"

# 验证地址识别
原文本: "地址：广州市高林路888号"
预期分类: "address"
预期参数名: "address"

# 验证邮箱识别
原文本: "邮箱：<EMAIL>"
预期分类: "email"
预期参数名: "email"
```

**验证点**：
- [ ] 电话号码识别准确率100%
- [ ] 地址信息识别准确率100%
- [ ] 邮箱地址识别准确率100%
- [ ] 普通文本正确分类

### 2.3 参数替换和预览测试

#### 2.3.1 预览生成接口测试
**接口**：`POST /api/parameter/preview`

**测试用例T-API-007**：生成参数化预览
```bash
# 请求
POST /api/parameter/preview
Content-Type: application/json
{
  "templateId": "2",
  "parameterDataId": "test-data-123"
}

# 预期响应
{
  "code": 200,
  "data": {
    "previewUrl": "/preview/parameter/test-data-123",
    "modifiedTemplateData": {
      // 替换后的模板数据
    }
  }
}
```

**验证点**：
- [ ] 能够生成预览URL
- [ ] 预览页面可正常访问
- [ ] 内容替换正确
- [ ] 样式保持不变

#### 2.3.2 参数化预览页面测试
**接口**：`GET /preview/parameter/:dataId`

**测试用例T-API-008**：访问预览页面
```bash
# 请求
GET /preview/parameter/test-data-123

# 预期响应
HTML页面，包含替换后的模板内容
```

**验证点**：
- [ ] 页面正常加载
- [ ] 内容替换正确
- [ ] 样式保持一致
- [ ] 富文本格式正确

### 2.4 图片生成服务测试

#### 2.4.1 参数化截图测试
**接口**：`GET /api/screenshots`

**测试用例T-API-009**：生成参数化图片
```bash
# 请求
GET /api/screenshots?parameterDataId=test-data-123&width=1242&height=2208&type=file&size=2&quality=0.9

# 预期响应
{
  "code": 200,
  "data": {
    "url": "生成的图片URL",
    "width": 1242,
    "height": 2208,
    "size": "文件大小"
  }
}
```

**验证点**：
- [ ] 图片生成成功
- [ ] 图片尺寸正确
- [ ] 图片质量符合要求
- [ ] 内容替换正确

#### 2.4.2 批量生成测试
**接口**：`POST /api/parameter/batch-generate`

**测试用例T-API-010**：批量生成图片
```bash
# 请求
POST /api/parameter/batch-generate
Content-Type: application/json
{
  "dataIds": ["test-data-123", "test-data-124", "test-data-125"],
  "outputOptions": {
    "width": 1242,
    "height": 2208,
    "type": "file",
    "size": 2,
    "quality": 0.9
  }
}

# 预期响应
{
  "code": 200,
  "data": {
    "batchId": "batch_1642320000000_abc123",
    "status": "processing"
  }
}
```

**验证点**：
- [ ] 批量任务创建成功
- [ ] 返回批量ID
- [ ] 异步处理正常

## 3. 功能测试

### 3.1 模板解析准确性测试

**测试用例T-FUNC-001**：模板ID=2解析验证
- **输入**：模板ID=2
- **预期输出**：识别出5个文本元素
- **验证点**：
  - [ ] 文本元素数量正确
  - [ ] UUID识别准确
  - [ ] 位置信息正确
  - [ ] 样式信息完整

**测试用例T-FUNC-002**：多页面模板解析
- **输入**：多页面模板数据
- **预期输出**：正确解析第一页内容
- **验证点**：
  - [ ] 多页面格式识别
  - [ ] 页面数据提取正确
  - [ ] 图层信息完整

### 3.2 参数替换正确性测试

**测试用例T-FUNC-003**：文本内容替换
- **原始文本**："你好,十二月"
- **替换文本**："你好,新年快乐"
- **验证点**：
  - [ ] 文本内容正确替换
  - [ ] 字体大小保持不变
  - [ ] 颜色保持不变
  - [ ] 对齐方式保持不变

**测试用例T-FUNC-004**：富文本格式保持
- **原始文本**："生活就像海洋，只有意志坚强的人，<br/>才能到达彼岸。——马克思"
- **替换文本**："成功不是终点，失败不是致命的，<br/>重要的是继续前进的勇气。——丘吉尔"
- **验证点**：
  - [ ] HTML标签保留
  - [ ] 换行格式正确
  - [ ] 特殊字符处理

### 3.3 图片生成质量测试

**测试用例T-FUNC-005**：图片质量验证
- **输入参数**：width=1242, height=2208, quality=0.9
- **验证点**：
  - [ ] 图片尺寸准确
  - [ ] 图片清晰度符合要求
  - [ ] 文字可读性良好
  - [ ] 颜色还原准确

## 4. 性能测试

### 4.1 响应时间测试

**测试用例T-PERF-001**：API响应时间
- **模板解析**：< 1秒
- **表单生成**：< 0.5秒
- **实时预览**：< 2秒
- **图片生成**：< 5秒

**测试方法**：
```bash
# 使用curl测试响应时间
time curl -X POST http://localhost:7001/api/template/parse \
  -H "Content-Type: application/json" \
  -d '{"templateId": "2"}'
```

### 4.2 并发测试

**测试用例T-PERF-002**：并发处理能力
- **用户表单访问**：1000个并发用户
- **预览渲染**：100个并发请求
- **图片生成**：50个并发请求

**测试工具**：Apache Bench (ab)
```bash
# 并发测试命令
ab -n 1000 -c 100 http://localhost:7001/api/templates
```

### 4.3 内存和CPU使用测试

**测试用例T-PERF-003**：资源使用监控
- **内存使用**：< 512MB
- **CPU使用**：< 80%
- **响应时间**：95%请求 < 2秒

## 5. 集成测试

### 5.1 端到端测试场景

**测试场景T-INT-001**：完整业务流程
1. 获取模板列表
2. 选择模板ID=2
3. 解析模板获取参数候选项
4. 配置参数（模拟主项目操作）
5. 填写用户数据（模拟主项目操作）
6. 生成预览
7. 生成最终图片

**验证点**：
- [ ] 整个流程无错误
- [ ] 数据传递正确
- [ ] 最终图片质量符合要求

### 5.2 外部API集成测试

**测试场景T-INT-002**：外部API调用
1. 模拟主项目API服务
2. 配置API认证
3. 测试数据获取
4. 测试错误处理
5. 测试重试机制

**验证点**：
- [ ] API调用成功
- [ ] 认证机制正常
- [ ] 错误处理正确
- [ ] 重试机制生效

## 6. 安全测试

### 6.1 输入验证测试

**测试用例T-SEC-001**：恶意输入防护
- **SQL注入测试**：在参数中注入SQL语句
- **XSS攻击测试**：在文本中注入脚本
- **文件路径遍历**：测试路径遍历攻击

**验证点**：
- [ ] 恶意输入被正确过滤
- [ ] 系统不受攻击影响
- [ ] 错误信息不泄露敏感信息

### 6.2 API安全测试

**测试用例T-SEC-002**：API安全验证
- **认证测试**：无效Token访问
- **授权测试**：越权访问测试
- **频率限制**：API调用频率限制

**验证点**：
- [ ] 无效认证被拒绝
- [ ] 越权访问被阻止
- [ ] 频率限制生效

## 7. 测试数据

### 7.1 标准测试数据

**模板数据**：基于模板ID=2
```json
{
  "templateId": "2",
  "title": "示例模板 - 日签插画手机海报"
}
```

**参数配置数据**：
```json
{
  "configId": "test-config-001",
  "templateId": "2",
  "parameters": [
    {
      "elementUuid": "98fd9b16db8a",
      "parameterName": "greeting",
      "parameterLabel": "问候语",
      "parameterType": "text",
      "isRequired": true,
      "isEnabled": true
    }
  ]
}
```

**用户填写数据**：
```json
{
  "dataId": "test-data-123",
  "configId": "test-config-001",
  "templateId": "2",
  "parameterValues": {
    "greeting": "你好,新年快乐",
    "quote": "成功不是终点，失败不是致命的，重要的是继续前进的勇气。",
    "contact": "电话：138-0000-0000\n地址：北京市朝阳区xxx路123号"
  }
}
```

## 8. 测试执行计划

### 8.1 测试阶段安排
- **第1-2周**：单元测试和API接口测试
- **第3-4周**：功能测试和集成测试
- **第5-6周**：性能测试和安全测试
- **第7周**：回归测试和缺陷修复
- **第8周**：验收测试和文档完善

### 8.2 测试环境准备
- **测试服务器**：独立的测试环境
- **测试数据**：标准化的测试数据集
- **测试工具**：Postman、Jest、Apache Bench
- **监控工具**：系统资源监控

### 8.3 缺陷管理
- **缺陷等级**：严重、重要、一般、轻微
- **修复优先级**：P0（立即修复）、P1（当天修复）、P2（3天内修复）、P3（版本内修复）
- **回归测试**：每次修复后进行相关功能回归测试

---

**文档版本**: v1.0  
**创建日期**: 2025-01-16  
**测试负责人**: 测试团队  
**审核状态**: 待审核
