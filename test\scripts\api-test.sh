#!/bin/bash

# 动态参数模板系统API批量测试脚本
# 使用curl命令测试所有API接口

set -e

# 配置
BASE_URL="http://localhost:7001"
TEST_TEMPLATE_ID="2"
TEST_DATA_ID="test-data-123"
OUTPUT_DIR="./results/api-test-results"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试函数
test_api() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local description="$4"
    local output_file="$5"
    
    log_info "测试: $description"
    
    if [ "$method" = "GET" ]; then
        curl -s -w "\n%{http_code}\n" \
             -H "Content-Type: application/json" \
             "$BASE_URL$endpoint" > "$output_file" 2>&1
    else
        curl -s -w "\n%{http_code}\n" \
             -X "$method" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "$BASE_URL$endpoint" > "$output_file" 2>&1
    fi
    
    # 检查HTTP状态码
    local http_code=$(tail -n1 "$output_file")
    local response_body=$(head -n -1 "$output_file")
    
    if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
        log_info "✓ $description - HTTP $http_code"
        echo "$response_body" > "$output_file"
        return 0
    else
        log_error "✗ $description - HTTP $http_code"
        echo "$response_body" > "$output_file"
        return 1
    fi
}

# 开始测试
log_info "开始API批量测试..."
log_info "基础URL: $BASE_URL"
log_info "输出目录: $OUTPUT_DIR"

# 1. 模板数据服务测试
log_info "=== 模板数据服务测试 ==="

# 1.1 获取模板列表
test_api "GET" "/api/templates" "" \
    "获取模板列表" \
    "$OUTPUT_DIR/templates_list.json"

# 1.2 获取模板列表（带分页）
test_api "GET" "/api/templates?page=1&pageSize=5" "" \
    "获取模板列表（分页）" \
    "$OUTPUT_DIR/templates_list_paged.json"

# 1.3 获取模板详情
test_api "GET" "/api/template/$TEST_TEMPLATE_ID" "" \
    "获取模板详情" \
    "$OUTPUT_DIR/template_detail.json"

# 1.4 获取模板预览
test_api "GET" "/api/template/$TEST_TEMPLATE_ID/preview" "" \
    "获取模板预览" \
    "$OUTPUT_DIR/template_preview.json"

# 2. 模板解析服务测试
log_info "=== 模板解析服务测试 ==="

# 2.1 解析模板
test_api "POST" "/api/template/parse" \
    "{\"templateId\": \"$TEST_TEMPLATE_ID\"}" \
    "解析模板内容" \
    "$OUTPUT_DIR/template_parse.json"

# 3. 参数化预览服务测试
log_info "=== 参数化预览服务测试 ==="

# 3.1 生成参数化预览
test_api "POST" "/api/parameter/preview" \
    "{\"templateId\": \"$TEST_TEMPLATE_ID\", \"parameterDataId\": \"$TEST_DATA_ID\"}" \
    "生成参数化预览" \
    "$OUTPUT_DIR/parameter_preview.json"

# 3.2 访问预览页面
test_api "GET" "/preview/parameter/$TEST_DATA_ID" "" \
    "访问预览页面" \
    "$OUTPUT_DIR/preview_page.html"

# 4. 图片生成服务测试
log_info "=== 图片生成服务测试 ==="

# 4.1 参数化截图
test_api "GET" "/api/screenshots?parameterDataId=$TEST_DATA_ID&width=1242&height=2208" "" \
    "生成参数化截图" \
    "$OUTPUT_DIR/screenshot.json"

# 4.2 批量生成图片
test_api "POST" "/api/parameter/batch-generate" \
    "{\"dataIds\": [\"$TEST_DATA_ID\"], \"outputOptions\": {\"width\": 1242, \"height\": 2208, \"type\": \"file\", \"size\": 2, \"quality\": 0.9}}" \
    "批量生成图片" \
    "$OUTPUT_DIR/batch_generate.json"

# 5. 错误处理测试
log_info "=== 错误处理测试 ==="

# 5.1 无效模板ID
test_api "GET" "/api/template/999999" "" \
    "无效模板ID测试" \
    "$OUTPUT_DIR/error_invalid_template.json"

# 5.2 缺少参数
test_api "POST" "/api/template/parse" \
    "{}" \
    "缺少参数测试" \
    "$OUTPUT_DIR/error_missing_params.json"

# 6. 性能测试
log_info "=== 性能测试 ==="

# 6.1 响应时间测试
log_info "测试API响应时间..."
time_start=$(date +%s.%N)
test_api "GET" "/api/templates" "" \
    "响应时间测试" \
    "$OUTPUT_DIR/performance_test.json"
time_end=$(date +%s.%N)
response_time=$(echo "$time_end - $time_start" | bc)
log_info "模板列表API响应时间: ${response_time}秒"

# 生成测试报告
log_info "=== 生成测试报告 ==="

cat > "$OUTPUT_DIR/test_report.md" << EOF
# API测试报告

## 测试时间
$(date)

## 测试环境
- 基础URL: $BASE_URL
- 测试模板ID: $TEST_TEMPLATE_ID
- 测试数据ID: $TEST_DATA_ID

## 测试结果

### 模板数据服务
- [x] 获取模板列表
- [x] 获取模板列表（分页）
- [x] 获取模板详情
- [x] 获取模板预览

### 模板解析服务
- [x] 解析模板内容

### 参数化预览服务
- [x] 生成参数化预览
- [x] 访问预览页面

### 图片生成服务
- [x] 生成参数化截图
- [x] 批量生成图片

### 错误处理
- [x] 无效模板ID处理
- [x] 缺少参数处理

### 性能指标
- 模板列表API响应时间: ${response_time}秒

## 详细结果
所有API响应结果已保存在当前目录的JSON文件中。
EOF

log_info "测试完成！"
log_info "测试报告: $OUTPUT_DIR/test_report.md"
log_info "详细结果: $OUTPUT_DIR/*.json"
