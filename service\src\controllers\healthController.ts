/**
 * 健康检查控制器
 * 提供系统健康状态和外部API连接状态查询
 */

import { send } from '../utils/tools'
import externalApiService from '../services/externalApi'

/**
 * 系统健康检查
 * GET /health
 */
export async function healthCheck(req: any, res: any) {
  try {
    const status = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      services: {
        database: 'healthy', // 如果有数据库连接可以在这里检查
        cache: 'healthy',
        externalApi: 'checking'
      }
    }

    // 检查外部API连接状态
    try {
      const apiStatus = await externalApiService.getConnectionStatus()
      status.services.externalApi = apiStatus.isConnected ? 'healthy' : 'unhealthy'
    } catch (error) {
      status.services.externalApi = 'unhealthy'
    }

    // 如果有任何服务不健康，返回503状态
    const isHealthy = Object.values(status.services).every(service => service === 'healthy')
    
    if (isHealthy) {
      send.success(res, status)
    } else {
      res.status(503)
      send.error(res, '部分服务不可用', 503)
    }
  } catch (error) {
    console.error('健康检查失败:', error)
    res.status(503)
    send.error(res, '健康检查失败', 503)
  }
}

/**
 * 外部API连接状态检查
 * GET /api/health/external-api
 */
export async function checkExternalApiStatus(req: any, res: any) {
  try {
    const status = await externalApiService.getConnectionStatus()
    
    const result = {
      service: 'external-api',
      ...status,
      cacheStats: externalApiService.getCacheStats()
    }

    if (status.isConnected) {
      send.success(res, result)
    } else {
      res.status(503)
      send.error(res, '外部API连接失败', 503)
    }
  } catch (error) {
    console.error('外部API状态检查失败:', error)
    res.status(503)
    send.error(res, '外部API状态检查失败', 503)
  }
}

/**
 * 缓存状态查询
 * GET /api/health/cache
 */
export async function getCacheStatus(req: any, res: any) {
  try {
    const cacheStats = externalApiService.getCacheStats()
    
    const result = {
      service: 'cache',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      stats: cacheStats
    }

    send.success(res, result)
  } catch (error) {
    console.error('缓存状态查询失败:', error)
    send.error(res, '缓存状态查询失败', 500)
  }
}

/**
 * 清理缓存
 * POST /api/health/cache/clear
 */
export async function clearCache(req: any, res: any) {
  try {
    externalApiService.clearCache()
    
    const result = {
      message: '缓存已清理',
      timestamp: new Date().toISOString()
    }

    send.success(res, result)
  } catch (error) {
    console.error('清理缓存失败:', error)
    send.error(res, '清理缓存失败', 500)
  }
}

export default {
  healthCheck,
  checkExternalApiStatus,
  getCacheStatus,
  clearCache
}
