/**
 * 外部API调用服务
 * 支持HTTP请求和响应处理，实现API认证机制、重试和缓存
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

/**
 * API响应接口
 */
export interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
  error?: any
}

/**
 * 重试配置接口
 */
export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffFactor: number
}

/**
 * 缓存项接口
 */
interface CacheItem<T = any> {
  data: T
  timestamp: number
  ttl: number
}

/**
 * 外部API服务类
 */
export class ExternalApiService {
  private client: AxiosInstance
  private cache: Map<string, CacheItem> = new Map()
  private retryConfig: RetryConfig
  private defaultCacheTTL: number = 5 * 60 * 1000 // 5分钟

  constructor(baseURL: string = 'http://localhost:7001', timeout: number = 10000) {
    this.client = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DynamicParameterTemplateSystem/1.0'
      }
    })

    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2
    }

    this.setupInterceptors()
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加认证头
        if (process.env.API_TOKEN) {
          config.headers['Authorization'] = `Bearer ${process.env.API_TOKEN}`
        }
        
        // 添加请求ID用于追踪
        config.headers['X-Request-ID'] = this.generateRequestId()
        
        console.log(`[API Request] ${config.method?.toUpperCase()} ${config.url}`)
        return config
      },
      (error) => {
        console.error('[API Request Error]', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[API Response] ${response.status} ${response.config.url}`)
        return response
      },
      (error) => {
        console.error(`[API Response Error] ${error.response?.status} ${error.config?.url}`, error.message)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 指数退避重试
   */
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    attempt: number = 0
  ): Promise<T> {
    try {
      return await operation()
    } catch (error) {
      if (attempt >= this.retryConfig.maxRetries) {
        throw error
      }

      // 计算延迟时间
      const delay = Math.min(
        this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, attempt),
        this.retryConfig.maxDelay
      )

      console.log(`[API Retry] Attempt ${attempt + 1}/${this.retryConfig.maxRetries}, delay: ${delay}ms`)
      
      await new Promise(resolve => setTimeout(resolve, delay))
      return this.retryWithBackoff(operation, attempt + 1)
    }
  }

  /**
   * 缓存管理
   */
  private getCacheKey(url: string, params?: any): string {
    const paramStr = params ? JSON.stringify(params) : ''
    return `${url}:${paramStr}`
  }

  private getFromCache<T>(key: string): T | null {
    const item = this.cache.get(key)
    if (!item) return null

    const now = Date.now()
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  private setCache<T>(key: string, data: T, ttl: number = this.defaultCacheTTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * GET请求
   */
  async get<T>(url: string, params?: any, useCache: boolean = true, cacheTTL?: number): Promise<ApiResponse<T>> {
    const cacheKey = this.getCacheKey(url, params)
    
    // 尝试从缓存获取
    if (useCache) {
      const cached = this.getFromCache<ApiResponse<T>>(cacheKey)
      if (cached) {
        console.log(`[API Cache Hit] ${url}`)
        return cached
      }
    }

    const response = await this.retryWithBackoff(async () => {
      return await this.client.get<ApiResponse<T>>(url, { params })
    })

    // 缓存响应
    if (useCache && response.data.code === 200) {
      this.setCache(cacheKey, response.data, cacheTTL)
    }

    return response.data
  }

  /**
   * POST请求
   */
  async post<T>(url: string, data?: any, useCache: boolean = false, cacheTTL?: number): Promise<ApiResponse<T>> {
    const cacheKey = this.getCacheKey(url, data)
    
    // POST请求通常不使用缓存，但某些场景可能需要
    if (useCache) {
      const cached = this.getFromCache<ApiResponse<T>>(cacheKey)
      if (cached) {
        console.log(`[API Cache Hit] ${url}`)
        return cached
      }
    }

    const response = await this.retryWithBackoff(async () => {
      return await this.client.post<ApiResponse<T>>(url, data)
    })

    // 缓存响应
    if (useCache && response.data.code === 200) {
      this.setCache(cacheKey, response.data, cacheTTL)
    }

    return response.data
  }

  /**
   * PUT请求
   */
  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    const response = await this.retryWithBackoff(async () => {
      return await this.client.put<ApiResponse<T>>(url, data)
    })

    return response.data
  }

  /**
   * DELETE请求
   */
  async delete<T>(url: string): Promise<ApiResponse<T>> {
    const response = await this.retryWithBackoff(async () => {
      return await this.client.delete<ApiResponse<T>>(url)
    })

    return response.data
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/health', { timeout: 5000 })
      return response.status === 200
    } catch (error) {
      console.error('[Health Check Failed]', error.message)
      return false
    }
  }

  /**
   * 获取连接状态
   */
  async getConnectionStatus(): Promise<{
    isConnected: boolean
    responseTime?: number
    lastCheck: string
    error?: string
  }> {
    const startTime = Date.now()
    const lastCheck = new Date().toISOString()

    try {
      const isConnected = await this.healthCheck()
      const responseTime = Date.now() - startTime

      return {
        isConnected,
        responseTime,
        lastCheck
      }
    } catch (error) {
      return {
        isConnected: false,
        lastCheck,
        error: error.message
      }
    }
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear()
    console.log('[Cache Cleared]')
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    size: number
    keys: string[]
  } {
    this.cleanExpiredCache()
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * 更新重试配置
   */
  updateRetryConfig(config: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...config }
  }
}

/**
 * 参数数据服务
 * 专门处理参数配置和用户数据的获取
 */
export class ParameterDataService {
  private apiService: ExternalApiService

  constructor(apiService: ExternalApiService) {
    this.apiService = apiService
  }

  /**
   * 获取参数配置
   */
  async getParameterConfig(configId: string): Promise<any> {
    try {
      const response = await this.apiService.get(`/api/parameter/config/${configId}`, null, true, 10 * 60 * 1000) // 缓存10分钟
      return response.data
    } catch (error) {
      console.error('获取参数配置失败:', error)
      throw new Error(`获取参数配置失败: ${error.message}`)
    }
  }

  /**
   * 获取用户填写数据
   */
  async getUserData(dataId: string): Promise<any> {
    try {
      const response = await this.apiService.get(`/api/parameter/data/${dataId}`, null, true, 5 * 60 * 1000) // 缓存5分钟
      return response.data
    } catch (error) {
      console.error('获取用户数据失败:', error)
      throw new Error(`获取用户数据失败: ${error.message}`)
    }
  }

  /**
   * 验证参数数据格式
   */
  validateParameterData(data: any, config: any): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (!data || typeof data !== 'object') {
      errors.push('参数数据格式无效')
      return { isValid: false, errors }
    }

    if (!config || !config.parameters) {
      errors.push('参数配置无效')
      return { isValid: false, errors }
    }

    // 验证必填参数
    for (const param of config.parameters) {
      if (param.isRequired && !data[param.parameterName]) {
        errors.push(`必填参数 ${param.parameterLabel} 不能为空`)
      }

      // 验证数据类型和长度
      const value = data[param.parameterName]
      if (value) {
        if (param.validationRules) {
          const rules = param.validationRules

          // 长度验证
          if (rules.maxLength && value.length > rules.maxLength) {
            errors.push(`参数 ${param.parameterLabel} 长度不能超过 ${rules.maxLength} 个字符`)
          }

          if (rules.minLength && value.length < rules.minLength) {
            errors.push(`参数 ${param.parameterLabel} 长度不能少于 ${rules.minLength} 个字符`)
          }

          // 正则验证
          if (rules.pattern) {
            const regex = new RegExp(rules.pattern)
            if (!regex.test(value)) {
              errors.push(`参数 ${param.parameterLabel} 格式不正确`)
            }
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 转换参数数据格式
   */
  transformParameterData(userData: any, config: any): any {
    const transformed: any = {}

    if (!config || !config.parameters) {
      return userData
    }

    for (const param of config.parameters) {
      const value = userData[param.parameterName]
      if (value !== undefined) {
        // 根据参数类型进行转换
        switch (param.parameterType) {
          case 'email':
            transformed[param.parameterName] = String(value).toLowerCase().trim()
            break
          case 'phone':
            transformed[param.parameterName] = String(value).replace(/\s+/g, '')
            break
          case 'textarea':
            // 保持换行符
            transformed[param.parameterName] = String(value).replace(/\n/g, '<br/>')
            break
          default:
            transformed[param.parameterName] = String(value).trim()
        }
      } else if (param.defaultValue) {
        transformed[param.parameterName] = param.defaultValue
      }
    }

    return transformed
  }
}

// 创建默认实例
const defaultApiService = new ExternalApiService()
export const parameterDataService = new ParameterDataService(defaultApiService)
export default defaultApiService
