# Simple API Test Script

$BASE_URL = "http://localhost:7001"
$OUTPUT_DIR = "./test/results"

# Create output directory
if (!(Test-Path $OUTPUT_DIR)) {
    New-Item -ItemType Directory -Path $OUTPUT_DIR -Force | Out-Null
}

Write-Host "Starting API Tests..." -ForegroundColor Green

# Test 1: Get Templates
Write-Host "1. Testing GET /api/templates" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/api/templates" -Method GET
    Write-Host "   Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
    $response.Content | Out-File "$OUTPUT_DIR/templates.json" -Encoding UTF8
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get Template Detail
Write-Host "2. Testing GET /api/template?id=2" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/api/template?id=2" -Method GET
    Write-Host "   Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
    $response.Content | Out-File "$OUTPUT_DIR/template_detail.json" -Encoding UTF8
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Parse Template
Write-Host "3. Testing POST /api/template/parse" -ForegroundColor Yellow
try {
    $body = '{"templateId": "2"}'
    $response = Invoke-WebRequest -Uri "$BASE_URL/api/template/parse" -Method POST -Body $body -ContentType "application/json"
    Write-Host "   Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
    $response.Content | Out-File "$OUTPUT_DIR/template_parse.json" -Encoding UTF8
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Parameter Replace
Write-Host "4. Testing POST /api/parameter/replace" -ForegroundColor Yellow
try {
    $body = '{"templateId": "2", "parameterDataId": "test-data-123"}'
    $response = Invoke-WebRequest -Uri "$BASE_URL/api/parameter/replace" -Method POST -Body $body -ContentType "application/json"
    Write-Host "   Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
    $response.Content | Out-File "$OUTPUT_DIR/parameter_replace.json" -Encoding UTF8
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Preview Page
Write-Host "5. Testing GET /preview/parameter/test-data-123" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/preview/parameter/test-data-123" -Method GET
    Write-Host "   Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
    $response.Content | Out-File "$OUTPUT_DIR/preview_page.html" -Encoding UTF8
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Batch Generate
Write-Host "6. Testing POST /api/parameter/batch-generate" -ForegroundColor Yellow
try {
    $body = '{"dataIds": ["test-data-123"], "outputOptions": {"width": 1242, "height": 2208, "type": "file", "quality": 0.9}}'
    $response = Invoke-WebRequest -Uri "$BASE_URL/api/parameter/batch-generate" -Method POST -Body $body -ContentType "application/json"
    Write-Host "   Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
    $response.Content | Out-File "$OUTPUT_DIR/batch_generate.json" -Encoding UTF8
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Health Check
Write-Host "7. Testing GET /health" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/health" -Method GET
    Write-Host "   Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
    $response.Content | Out-File "$OUTPUT_DIR/health.json" -Encoding UTF8
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Cache Status
Write-Host "8. Testing GET /api/health/cache" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$BASE_URL/api/health/cache" -Method GET
    Write-Host "   Status: $($response.StatusCode) - SUCCESS" -ForegroundColor Green
    $response.Content | Out-File "$OUTPUT_DIR/cache_status.json" -Encoding UTF8
} catch {
    Write-Host "   ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nAll tests completed! Results saved to $OUTPUT_DIR" -ForegroundColor Green
