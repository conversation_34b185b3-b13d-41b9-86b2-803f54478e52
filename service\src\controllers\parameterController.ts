/**
 * 参数化功能控制器
 * 提供参数替换和预览生成API接口
 */

import { send } from '../utils/tools'
import parameterEngineService from '../services/parameterEngine'

/**
 * 生成参数化预览
 * POST /api/parameter/preview
 */
export async function generatePreview(req: any, res: any) {
  try {
    const { templateId, parameterDataId } = req.body
    
    // 参数验证
    if (!templateId) {
      return send.error(res, '模板ID不能为空', 400)
    }
    
    if (!parameterDataId) {
      return send.error(res, '参数数据ID不能为空', 400)
    }
    
    // 生成预览
    const result = await parameterEngineService.generatePreview(templateId, parameterDataId)
    
    send.success(res, result)
  } catch (error) {
    console.error('生成预览失败:', error)
    
    if (error.message.includes('不存在')) {
      return send.error(res, error.message, 404)
    }
    
    send.error(res, '生成预览失败', 500)
  }
}

/**
 * 执行参数替换
 * POST /api/parameter/replace
 */
export async function replaceParameters(req: any, res: any) {
  try {
    const { templateId, parameterDataId } = req.body
    
    // 参数验证
    if (!templateId) {
      return send.error(res, '模板ID不能为空', 400)
    }
    
    if (!parameterDataId) {
      return send.error(res, '参数数据ID不能为空', 400)
    }
    
    // 执行替换
    const result = await parameterEngineService.replaceParameters(templateId, parameterDataId)
    
    if (result.success) {
      send.success(res, result)
    } else {
      send.error(res, `参数替换失败: ${result.errors.join(', ')}`, 400)
    }
  } catch (error) {
    console.error('参数替换失败:', error)
    
    if (error.message.includes('不存在')) {
      return send.error(res, error.message, 404)
    }
    
    send.error(res, '参数替换失败', 500)
  }
}

/**
 * 获取预览页面
 * GET /preview/parameter/:dataId
 */
export async function getPreviewPage(req: any, res: any) {
  try {
    const { dataId } = req.params
    
    if (!dataId) {
      return res.status(400).send('参数数据ID不能为空')
    }
    
    // 生成预览页面HTML
    const html = generatePreviewPageHTML(dataId)
    
    res.setHeader('Content-Type', 'text/html; charset=utf-8')
    res.send(html)
  } catch (error) {
    console.error('获取预览页面失败:', error)
    res.status(500).send('获取预览页面失败')
  }
}

/**
 * 生成预览页面HTML
 */
function generatePreviewPageHTML(dataId: string): string {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参数化模板预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .preview-area {
            padding: 20px;
            text-align: center;
        }
        .loading {
            padding: 40px;
            color: #666;
        }
        .error {
            padding: 40px;
            color: #e74c3c;
            background: #fdf2f2;
            border: 1px solid #fecaca;
            border-radius: 4px;
            margin: 20px;
        }
        .canvas-container {
            display: inline-block;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        #preview-canvas {
            display: block;
            max-width: 100%;
            height: auto;
        }
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            text-align: left;
        }
        .info h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .info p {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>参数化模板预览</h1>
            <p>数据ID: ${dataId}</p>
        </div>
        <div class="preview-area">
            <div class="loading" id="loading">
                正在加载预览...
            </div>
            <div id="preview-content" style="display: none;">
                <div class="canvas-container">
                    <canvas id="preview-canvas"></canvas>
                </div>
                <div class="info">
                    <h3>预览信息</h3>
                    <p><strong>模板ID:</strong> <span id="template-id">-</span></p>
                    <p><strong>生成时间:</strong> <span id="generated-at">-</span></p>
                    <p><strong>尺寸:</strong> <span id="dimensions">-</span></p>
                    <p><strong>替换元素:</strong> <span id="replaced-elements">-</span></p>
                </div>
            </div>
            <div id="error-content" style="display: none;">
                <div class="error">
                    <h3>预览生成失败</h3>
                    <p id="error-message">未知错误</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟预览数据加载
        setTimeout(() => {
            const loading = document.getElementById('loading');
            const previewContent = document.getElementById('preview-content');
            const errorContent = document.getElementById('error-content');
            
            try {
                // 模拟成功加载
                loading.style.display = 'none';
                previewContent.style.display = 'block';
                
                // 设置预览信息
                document.getElementById('template-id').textContent = '2';
                document.getElementById('generated-at').textContent = new Date().toLocaleString();
                document.getElementById('dimensions').textContent = '1242 x 2208';
                document.getElementById('replaced-elements').textContent = '6个文本元素';
                
                // 创建模拟画布
                const canvas = document.getElementById('preview-canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = 400;
                canvas.height = 600;
                
                // 绘制模拟预览
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('参数化模板预览', canvas.width/2, 50);
                
                ctx.fillStyle = '#34495e';
                ctx.font = '16px Arial';
                ctx.fillText('数据ID: ${dataId}', canvas.width/2, 80);
                
                ctx.fillStyle = '#e74c3c';
                ctx.font = 'bold 32px Arial';
                ctx.fillText('你好,新年快乐', canvas.width/2, 150);
                
                ctx.fillStyle = '#27ae60';
                ctx.font = '14px Arial';
                ctx.fillText('成功不是终点，失败不是致命的', canvas.width/2, 200);
                ctx.fillText('重要的是继续前进的勇气', canvas.width/2, 220);
                
                ctx.fillStyle = '#8e44ad';
                ctx.font = '18px Arial';
                ctx.fillText('#新年快乐#', canvas.width/2, 280);
                
                ctx.fillStyle = '#666';
                ctx.font = '12px Arial';
                ctx.fillText('电话：138-0000-0000', canvas.width/2, 450);
                ctx.fillText('地址：北京市朝阳区xxx路123号', canvas.width/2, 470);
                ctx.fillText('邮箱：<EMAIL>', canvas.width/2, 490);
                
                ctx.fillStyle = '#95a5a6';
                ctx.font = '10px Arial';
                ctx.fillText('这是一个参数化模板预览示例', canvas.width/2, 550);
                
            } catch (error) {
                loading.style.display = 'none';
                errorContent.style.display = 'block';
                document.getElementById('error-message').textContent = error.message;
            }
        }, 1500);
    </script>
</body>
</html>
  `
}

export default {
  generatePreview,
  replaceParameters,
  getPreviewPage
}
